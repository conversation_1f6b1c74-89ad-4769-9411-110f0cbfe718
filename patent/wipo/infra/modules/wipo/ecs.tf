# ECS Task Definition for Patent Scraper
resource "aws_ecs_task_definition" "patent_scraper" {
  family                   = "${var.prefix}-patent-scraper"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = "2048"  # 2 vCPU
  memory                   = "4096"  # 4 GB
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn

  runtime_platform {
    operating_system_family = "LINUX"
    cpu_architecture        = var.architectures[0] == "arm64" ? "ARM64" : "X86_64"
  }

  container_definitions = jsonencode([
    {
      name  = "patent-scraper"
      image = "${aws_ecr_repository.patent_scraper.repository_url}:latest"
      
      essential = true
      
      environment = [
        {
          name  = "AWS_REGION"
          value = var.aws_region
        },
        {
          name  = "PATENT_BULKDATA_BUCKET_NAME"
          value = var.patent_bulkdata_bucket_name
        },
        {
          name  = "ADP_BUCKET_NAME"
          value = var.adp_bucket_name
        },
        {
          name  = "RUST_LOG"
          value = "info"
        },
        {
          name  = "IS_LOCAL"
          value = "false"
        },
        {
          name  = "DEMO"
          value = "false"
        },
        {
          name  = "CHROME_BIN"
          value = "/usr/bin/google-chrome-stable"
        },
        {
          name  = "CHROME_PATH"
          value = "/usr/bin/google-chrome-stable"
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.patent_scraper.name
          "awslogs-region"        = var.aws_region
          "awslogs-stream-prefix" = "ecs"
        }
      }

      # Chrome needs more resources and specific capabilities
      linuxParameters = {
        capabilities = {
          add = ["SYS_ADMIN"]
        }
        devices = []
        initProcessEnabled = true
      }

      # Health check (optional)
      healthCheck = {
        command = ["CMD-SHELL", "echo 'healthy'"]
        interval = 30
        timeout = 5
        retries = 3
        startPeriod = 60
      }
    }
  ])

  tags = var.tags
}

# StepFunctions用のECS実行ポリシー
resource "aws_iam_policy" "stepfunctions_ecs_policy" {
  name        = "${var.prefix}-stepfunctions-ecs-policy"
  description = "Policy for StepFunctions to execute ECS tasks"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:RunTask",
          "ecs:StopTask",
          "ecs:DescribeTasks"
        ]
        Resource = [
          aws_ecs_task_definition.patent_scraper.arn,
          "arn:aws:ecs:${var.aws_region}:${var.account_id}:task/${aws_ecs_cluster.wipo_cluster.name}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "ecs:DescribeTaskDefinition"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "iam:PassRole"
        ]
        Resource = [
          aws_iam_role.ecs_task_execution_role.arn,
          aws_iam_role.ecs_task_role.arn
        ]
      }
    ]
  })

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "stepfunctions_ecs_policy" {
  role       = aws_iam_role.stepfunctions_execution_role.name
  policy_arn = aws_iam_policy.stepfunctions_ecs_policy.arn
}
