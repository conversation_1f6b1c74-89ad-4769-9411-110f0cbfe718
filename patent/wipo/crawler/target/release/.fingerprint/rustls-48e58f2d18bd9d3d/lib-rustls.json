{"rustc": 5357548097637079788, "features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1988539120246850232, "path": 17539124407969537600, "deps": [[3722963349756955755, "once_cell", false, 12906090134488783275], [6528079939221783635, "zeroize", false, 1449833509155880236], [8880649100394045925, "build_script_build", false, 2255731453114954633], [11782200238436109817, "<PERSON><PERSON><PERSON>", false, 347587654286841809], [16009134724182327169, "pki_types", false, 16639563445985851306], [16944451698427853066, "aws_lc_rs", false, 12692718604363076675], [17003143334332120809, "subtle", false, 12024614941358870138]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustls-48e58f2d18bd9d3d/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}