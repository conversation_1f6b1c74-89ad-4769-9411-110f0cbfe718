{"rustc": 5357548097637079788, "features": "[]", "declared_features": "[\"behavior-version-latest\", \"default\", \"default-https-client\", \"gated-tests\", \"rt-tokio\", \"rustls\", \"test-util\"]", "target": 17568142281239487742, "profile": 2040997289075261528, "path": 10400339153932743345, "deps": [[597652819505411233, "aws_smithy_async", false, 12795937184814256765], [628335450098706537, "aws_types", false, 5990255761710575598], [1703701203904756964, "aws_smithy_http", false, 12198927930044970465], [1924610461715385833, "aws_runtime", false, 17770559869940480155], [4405182208873388884, "http", false, 4671494172259030090], [8606274917505247608, "tracing", false, 12644509248699729773], [8732774447830947144, "aws_credential_types", false, 11743635377460730417], [8980948081804773267, "aws_smithy_runtime", false, 1338039813942388947], [10535238618256657085, "aws_smithy_types", false, 16723610761838236708], [12285238697122577036, "fastrand", false, 14841645550586359616], [16066129441945555748, "bytes", false, 16328044339417842794], [16224642111061375652, "regex_lite", false, 5884541101141420545], [18194352178806123594, "aws_smithy_runtime_api", false, 2093103239404377582], [18328120035545265972, "aws_smithy_json", false, 15660876392921206843]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/aws-sdk-ssooidc-0d3b0887c6da20ef/dep-lib-aws_sdk_ssooidc", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}