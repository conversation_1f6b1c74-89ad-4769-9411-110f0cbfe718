{"rustc": 5357548097637079788, "features": "[\"arrow-format\", \"base64\", \"default\", \"fallible-streaming-iterator\", \"futures\", \"io_ipc\", \"io_parquet\", \"parquet2\", \"streaming-iterator\"]", "declared_features": "[\"arrow\", \"arrow-array\", \"arrow-buffer\", \"arrow-data\", \"arrow-format\", \"arrow-schema\", \"async-stream\", \"avro-schema\", \"base64\", \"benchmarks\", \"chrono-tz\", \"comfy-table\", \"compute\", \"compute_aggregate\", \"compute_arithmetics\", \"compute_arithmetics_decimal\", \"compute_bitwise\", \"compute_boolean\", \"compute_boolean_kleene\", \"compute_cast\", \"compute_comparison\", \"compute_concatenate\", \"compute_contains\", \"compute_filter\", \"compute_hash\", \"compute_if_then_else\", \"compute_length\", \"compute_like\", \"compute_limit\", \"compute_merge_sort\", \"compute_nullif\", \"compute_partition\", \"compute_regex_match\", \"compute_sort\", \"compute_substring\", \"compute_take\", \"compute_temporal\", \"compute_utf8\", \"compute_window\", \"csv\", \"csv-async\", \"csv-core\", \"default\", \"fallible-streaming-iterator\", \"full\", \"futures\", \"hashbrown\", \"hex\", \"indexmap\", \"io_avro\", \"io_avro_async\", \"io_avro_compression\", \"io_csv\", \"io_csv_async\", \"io_csv_read\", \"io_csv_read_async\", \"io_csv_write\", \"io_flight\", \"io_ipc\", \"io_ipc_compression\", \"io_ipc_read_async\", \"io_ipc_write_async\", \"io_json\", \"io_json_integration\", \"io_json_read\", \"io_json_write\", \"io_odbc\", \"io_orc\", \"io_parquet\", \"io_parquet_bloom_filter\", \"io_parquet_brotli\", \"io_parquet_compression\", \"io_parquet_gzip\", \"io_parquet_lz4\", \"io_parquet_lz4_flex\", \"io_parquet_sample_test\", \"io_parquet_snappy\", \"io_parquet_zstd\", \"io_print\", \"itertools\", \"json-deserializer\", \"lexical-core\", \"lz4\", \"multiversion\", \"odbc-api\", \"orc-format\", \"parquet2\", \"rand\", \"regex\", \"regex-syntax\", \"serde\", \"serde_derive\", \"serde_json\", \"serde_types\", \"simd\", \"streaming-iterator\", \"strength_reduce\", \"zstd\"]", "target": 3501116842186792193, "profile": 2040997289075261528, "path": 3723669406038409559, "deps": [[54896445063299169, "hash_hasher", false, 6165064217622452634], [1777805956335068443, "build_script_build", false, 10315162281185994789], [2706460456408817945, "futures", false, 11066807749335416508], [5157631553186200874, "num_traits", false, 15172264317736971845], [5510864063823219921, "fallible_streaming_iterator", false, 6884973006350233040], [6511429716036861196, "bytemuck", false, 3619568640106198329], [6547980334806251551, "chrono", false, 3087184771843276092], [7898571650830454567, "ethnum", false, 13586121282191043993], [8067010153367330186, "simdutf8", false, 5500670664699687485], [9122563107207267705, "dyn_clone", false, 13657054283658761102], [9235208004366183979, "streaming_iterator", false, 1778443520573682972], [9938377527623146791, "foreign_vec", false, 4835562925616905732], [10791833957791020630, "ahash", false, 15660386110382087463], [11073574711591056092, "parquet2", false, 12090797530564491462], [12170264697963848012, "either", false, 11288880356043486469], [15129570801809786797, "arrow_format", false, 4712913544319326473], [18066890886671768183, "base64", false, 12486579130706063238]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/arrow2-a33429d45f7c1eb6/dep-lib-arrow2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}