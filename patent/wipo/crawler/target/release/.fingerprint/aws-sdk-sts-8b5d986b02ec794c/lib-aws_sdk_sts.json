{"rustc": 5357548097637079788, "features": "[]", "declared_features": "[\"behavior-version-latest\", \"default\", \"default-https-client\", \"gated-tests\", \"rt-tokio\", \"rustls\", \"test-util\"]", "target": 2815721327362910856, "profile": 2040997289075261528, "path": 16918219432059016550, "deps": [[597652819505411233, "aws_smithy_async", false, 12795937184814256765], [628335450098706537, "aws_types", false, 5990255761710575598], [1703701203904756964, "aws_smithy_http", false, 12198927930044970465], [1924610461715385833, "aws_runtime", false, 17770559869940480155], [4405182208873388884, "http", false, 4671494172259030090], [7566079343924392875, "aws_smithy_query", false, 8008787564299205480], [8606274917505247608, "tracing", false, 12644509248699729773], [8732774447830947144, "aws_credential_types", false, 11743635377460730417], [8980948081804773267, "aws_smithy_runtime", false, 1338039813942388947], [10535238618256657085, "aws_smithy_types", false, 16723610761838236708], [12285238697122577036, "fastrand", false, 14841645550586359616], [16224642111061375652, "regex_lite", false, 5884541101141420545], [17157196511762492864, "aws_smithy_xml", false, 6900987662449413579], [18194352178806123594, "aws_smithy_runtime_api", false, 2093103239404377582], [18328120035545265972, "aws_smithy_json", false, 15660876392921206843]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/aws-sdk-sts-8b5d986b02ec794c/dep-lib-aws_sdk_sts", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}