{"rustc": 5357548097637079788, "features": "[\"async\", \"async-stream\", \"bloom_filter\", \"brotli\", \"default\", \"flate2\", \"futures\", \"gzip\", \"lz4\", \"snap\", \"snappy\", \"xxhash-rust\", \"zstd\"]", "declared_features": "[\"async\", \"async-stream\", \"bloom_filter\", \"brotli\", \"default\", \"flate2\", \"full\", \"futures\", \"gzip\", \"gzip_zlib_ng\", \"lz4\", \"lz4_flex\", \"serde\", \"serde_types\", \"snap\", \"snappy\", \"xxhash-rust\", \"zstd\"]", "target": 2727170183322477332, "profile": 2040997289075261528, "path": 16308507028835935775, "deps": [[1188017320647144970, "async_stream", false, 9508357372081386152], [1804806304303030865, "xxhash_rust", false, 16547846449026768698], [2706460456408817945, "futures", false, 11066807749335416508], [7446387262633265650, "lz4", false, 6145807368915848291], [10563170702865159712, "flate2", false, 6785617758165314986], [11685816543203387617, "streaming_decompression", false, 18129095006086892265], [12378581237762097513, "brotli", false, 15260619154335472403], [12405711135109940338, "seq_macro", false, 2470245660566670494], [15566300943691501420, "zstd", false, 13827775214611925243], [16121279737985519728, "parquet_format_safe", false, 892565067393788646], [18412833500638531047, "snap", false, 10104098283310677342]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/parquet2-ccb1d275bbfb594c/dep-lib-parquet2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}