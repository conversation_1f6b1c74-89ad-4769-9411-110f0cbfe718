{"rustc": 5357548097637079788, "features": "[\"default\", \"offline\"]", "declared_features": "[\"default\", \"directories\", \"fetch\", \"native-tls\", \"nightly\", \"offline\", \"rustls\", \"ureq\", \"walkdir\", \"zip\"]", "target": 6765718376882695235, "profile": 2040997289075261528, "path": 2931142143358800774, "deps": [[895066815307484583, "which", false, 15238197870114234929], [2098583196738611028, "rand", false, 6170125719090810299], [3150220818285335163, "url", false, 15864548330171814397], [5986029879202738730, "log", false, 16162741913367429548], [6219554740863759696, "derive_builder", false, 16888501973066869857], [9689903380558560274, "serde", false, 106362935358965852], [10806645703491011684, "thiserror", false, 3523988299172249027], [12261610614527126074, "tempfile", false, 15502760724881977624], [13077212702700853852, "base64", false, 3075890672103716243], [13359290672898815396, "regex", false, 9932314271437277898], [14843943393121946134, "build_script_build", false, 10464149370696947007], [15367738274754116744, "serde_json", false, 5336973750309629001], [17932841424857612953, "tungstenite", false, 9354569731711513494], [18396545099218507036, "anyhow", false, 14208891841533054200]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/headless_chrome-219b63b64a35dadf/dep-lib-headless_chrome", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}