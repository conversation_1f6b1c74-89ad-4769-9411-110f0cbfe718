{"$message_type":"diagnostic","message":"unused import: `log::info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handler/s3_handler.rs","byte_start":90,"byte_end":99,"line_start":3,"line_end":3,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use log::info;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/handler/s3_handler.rs","byte_start":86,"byte_end":101,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use log::info;","highlight_start":1,"highlight_end":15},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `log::info`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handler/s3_handler.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::info;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `generate_csv_data`, `navigate_to_documents_tab`, `parquet_writer::save_patent_data_to_parquet`, `pdf_downloader::extract_pdf_url`, `save_as_csv`, `upload_csv_data_to_s3`, `upload_file_to_s3`, and `upload_parquet_data_to_s3`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handler/mod.rs","byte_start":230,"byte_end":255,"line_start":10,"line_end":10,"column_start":57,"column_end":82,"is_primary":true,"text":[{"text":"        close_tab, new_browser, navigate_to_claims_tab, navigate_to_documents_tab,","highlight_start":57,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handler/mod.rs","byte_start":407,"byte_end":438,"line_start":16,"line_end":16,"column_start":5,"column_end":36,"is_primary":true,"text":[{"text":"    pdf_downloader::extract_pdf_url,","highlight_start":5,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handler/mod.rs","byte_start":444,"byte_end":487,"line_start":17,"line_end":17,"column_start":5,"column_end":48,"is_primary":true,"text":[{"text":"    parquet_writer::save_patent_data_to_parquet,","highlight_start":5,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handler/mod.rs","byte_start":506,"byte_end":523,"line_start":18,"line_end":18,"column_start":18,"column_end":35,"is_primary":true,"text":[{"text":"    csv_writer::{generate_csv_data, save_as_csv},","highlight_start":18,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handler/mod.rs","byte_start":525,"byte_end":536,"line_start":18,"line_end":18,"column_start":37,"column_end":48,"is_primary":true,"text":[{"text":"    csv_writer::{generate_csv_data, save_as_csv},","highlight_start":37,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handler/mod.rs","byte_start":565,"byte_end":582,"line_start":20,"line_end":20,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"        upload_file_to_s3, upload_csv_data_to_s3, upload_parquet_data_to_s3,","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handler/mod.rs","byte_start":584,"byte_end":605,"line_start":20,"line_end":20,"column_start":28,"column_end":49,"is_primary":true,"text":[{"text":"        upload_file_to_s3, upload_csv_data_to_s3, upload_parquet_data_to_s3,","highlight_start":28,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handler/mod.rs","byte_start":607,"byte_end":632,"line_start":20,"line_end":20,"column_start":51,"column_end":76,"is_primary":true,"text":[{"text":"        upload_file_to_s3, upload_csv_data_to_s3, upload_parquet_data_to_s3,","highlight_start":51,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/handler/mod.rs","byte_start":228,"byte_end":255,"line_start":10,"line_end":10,"column_start":55,"column_end":82,"is_primary":true,"text":[{"text":"        close_tab, new_browser, navigate_to_claims_tab, navigate_to_documents_tab,","highlight_start":55,"highlight_end":82}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handler/mod.rs","byte_start":401,"byte_end":639,"line_start":15,"line_end":21,"column_start":6,"column_end":6,"is_primary":true,"text":[{"text":"    },","highlight_start":6,"highlight_end":7},{"text":"    pdf_downloader::extract_pdf_url,","highlight_start":1,"highlight_end":37},{"text":"    parquet_writer::save_patent_data_to_parquet,","highlight_start":1,"highlight_end":49},{"text":"    csv_writer::{generate_csv_data, save_as_csv},","highlight_start":1,"highlight_end":50},{"text":"    s3_handler::{","highlight_start":1,"highlight_end":18},{"text":"        upload_file_to_s3, upload_csv_data_to_s3, upload_parquet_data_to_s3,","highlight_start":1,"highlight_end":77},{"text":"    },","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `generate_csv_data`, `navigate_to_documents_tab`, `parquet_writer::save_patent_data_to_parquet`, `pdf_downloader::extract_pdf_url`, `save_as_csv`, `upload_csv_data_to_s3`, `upload_file_to_s3`, and `upload_parquet_data_to_s3`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handler/mod.rs:10:57\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        close_tab, new_browser, navigate_to_claims_tab, navigate_to_documents_tab,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pdf_downloader::extract_pdf_url,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    parquet_writer::save_patent_data_to_parquet,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    csv_writer::{generate_csv_data, save_as_csv},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    s3_handler::{\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        upload_file_to_s3, upload_csv_data_to_s3, upload_parquet_data_to_s3,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `filter_by_states`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/jobs/patent_processor.rs","byte_start":141,"byte_end":157,"line_start":5,"line_end":5,"column_start":75,"column_end":91,"is_primary":true,"text":[{"text":"use crate::domains::{PatentData, DesignatedStates, new_designated_states, filter_by_states};","highlight_start":75,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/jobs/patent_processor.rs","byte_start":139,"byte_end":157,"line_start":5,"line_end":5,"column_start":73,"column_end":91,"is_primary":true,"text":[{"text":"use crate::domains::{PatentData, DesignatedStates, new_designated_states, filter_by_states};","highlight_start":73,"highlight_end":91}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `filter_by_states`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/jobs/patent_processor.rs:5:75\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domains::{PatentData, DesignatedStates, new_designated_states, filter_by_states};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `designated_states_list`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/jobs/patent_processor.rs","byte_start":1498,"byte_end":1520,"line_start":35,"line_end":35,"column_start":60,"column_end":82,"is_primary":true,"text":[{"text":"async fn process_wo_numbers_local(wo_numbers: Vec<String>, designated_states_list: &DesignatedStates) -> Result<(Vec<PatentData>, usize)> {","highlight_start":60,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/jobs/patent_processor.rs","byte_start":1498,"byte_end":1520,"line_start":35,"line_end":35,"column_start":60,"column_end":82,"is_primary":true,"text":[{"text":"async fn process_wo_numbers_local(wo_numbers: Vec<String>, designated_states_list: &DesignatedStates) -> Result<(Vec<PatentData>, usize)> {","highlight_start":60,"highlight_end":82}],"label":null,"suggested_replacement":"_designated_states_list","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `designated_states_list`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/jobs/patent_processor.rs:35:60\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0ml(wo_numbers: Vec<String>, designated_states_list: &DesignatedStates) -> Result<(Vec<Patent\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_designated_states_list`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/jobs/patent_processor.rs","byte_start":1621,"byte_end":1638,"line_start":37,"line_end":37,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    let mut skipped_count = 0;","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/jobs/patent_processor.rs","byte_start":1621,"byte_end":1625,"line_start":37,"line_end":37,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut skipped_count = 0;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/jobs/patent_processor.rs:37:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut skipped_count = 0;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `designated_states_list`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/jobs/patent_processor.rs","byte_start":3314,"byte_end":3336,"line_start":72,"line_end":72,"column_start":67,"column_end":89,"is_primary":true,"text":[{"text":"async fn process_wo_numbers_with_browser(wo_numbers: Vec<String>, designated_states_list: &DesignatedStates) -> Result<(Vec<PatentData>, usize)> {","highlight_start":67,"highlight_end":89}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/jobs/patent_processor.rs","byte_start":3314,"byte_end":3336,"line_start":72,"line_end":72,"column_start":67,"column_end":89,"is_primary":true,"text":[{"text":"async fn process_wo_numbers_with_browser(wo_numbers: Vec<String>, designated_states_list: &DesignatedStates) -> Result<(Vec<PatentData>, usize)> {","highlight_start":67,"highlight_end":89}],"label":null,"suggested_replacement":"_designated_states_list","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `designated_states_list`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/jobs/patent_processor.rs:72:67\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m72\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mr(wo_numbers: Vec<String>, designated_states_list: &DesignatedStates) -> Result<(Vec<Patent\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_designated_states_list`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/jobs/patent_processor.rs","byte_start":3437,"byte_end":3454,"line_start":74,"line_end":74,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    let mut skipped_count = 0;","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/jobs/patent_processor.rs","byte_start":3437,"byte_end":3441,"line_start":74,"line_end":74,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut skipped_count = 0;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/jobs/patent_processor.rs:74:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m74\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut skipped_count = 0;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `create_new_patent_data` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/domains/patent.rs","byte_start":2313,"byte_end":2335,"line_start":64,"line_end":64,"column_start":8,"column_end":30,"is_primary":true,"text":[{"text":"pub fn create_new_patent_data() -> PatentData {","highlight_start":8,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `create_new_patent_data` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/patent.rs:64:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m64\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn create_new_patent_data() -> PatentData {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `states` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/domains/designated_states.rs","byte_start":99,"byte_end":115,"line_start":3,"line_end":3,"column_start":12,"column_end":28,"is_primary":false,"text":[{"text":"pub struct DesignatedStates {","highlight_start":12,"highlight_end":28}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domains/designated_states.rs","byte_start":126,"byte_end":132,"line_start":4,"line_end":4,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    pub states: Vec<String>,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`DesignatedStates` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `states` is never read\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/designated_states.rs:4:9\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DesignatedStates {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub states: Vec<String>,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `DesignatedStates` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `filter_by_states` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/domains/designated_states.rs","byte_start":568,"byte_end":584,"line_start":21,"line_end":21,"column_start":8,"column_end":24,"is_primary":true,"text":[{"text":"pub fn filter_by_states(designated_states: &DesignatedStates, text: &str) -> bool {","highlight_start":8,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `filter_by_states` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/designated_states.rs:21:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn filter_by_states(designated_states: &DesignatedStates, text: &str) -> bool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `navigate_to_documents_tab` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/handler/browser_handler.rs","byte_start":2364,"byte_end":2389,"line_start":74,"line_end":74,"column_start":8,"column_end":33,"is_primary":true,"text":[{"text":"pub fn navigate_to_documents_tab(tab: &mut Arc<Tab>) -> Result<()> {","highlight_start":8,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `navigate_to_documents_tab` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handler/browser_handler.rs:74:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m74\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn navigate_to_documents_tab(tab: &mut Arc<Tab>) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `extract_pdf_url` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/handler/pdf_downloader.rs","byte_start":185,"byte_end":200,"line_start":9,"line_end":9,"column_start":14,"column_end":29,"is_primary":true,"text":[{"text":"pub async fn extract_pdf_url(document: &Html) -> Result<Option<String>> {","highlight_start":14,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `extract_pdf_url` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handler/pdf_downloader.rs:9:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn extract_pdf_url(document: &Html) -> Result<Option<String>> {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `download_pdf` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/handler/pdf_downloader.rs","byte_start":3743,"byte_end":3755,"line_start":78,"line_end":78,"column_start":14,"column_end":26,"is_primary":true,"text":[{"text":"pub async fn download_pdf(url: &str) -> Result<String, Box<dyn std::error::Error>> {","highlight_start":14,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `download_pdf` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handler/pdf_downloader.rs:78:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn download_pdf(url: &str) -> Result<String, Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `upload_file_to_s3` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/handler/s3_handler.rs","byte_start":115,"byte_end":132,"line_start":5,"line_end":5,"column_start":14,"column_end":31,"is_primary":true,"text":[{"text":"pub async fn upload_file_to_s3(file_path: &str, region: &str, bucket: &str, key: &str) -> Result<String, Box<dyn std::error::Error>> {","highlight_start":14,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `upload_file_to_s3` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handler/s3_handler.rs:5:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn upload_file_to_s3(file_path: &str, region: &str, bucket: &str, key: &str) -> Resul\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"14 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 14 warnings emitted\u001b[0m\n\n"}
