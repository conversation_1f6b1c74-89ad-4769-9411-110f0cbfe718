{"rustc": 5357548097637079788, "features": "[\"__tls\", \"blocking\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2040997289075261528, "path": 8058025078677484575, "deps": [[40386456601120721, "percent_encoding", false, 12648584351376803869], [95042085696191081, "ipnet", false, 13063611299244335318], [264090853244900308, "sync_wrapper", false, 8159360083051967048], [784494742817713399, "tower_service", false, 17704212177869149945], [1906322745568073236, "pin_project_lite", false, 3718068317022005989], [3150220818285335163, "url", false, 15864548330171814397], [3722963349756955755, "once_cell", false, 12906090134488783275], [4405182208873388884, "http", false, 4671494172259030090], [5138218615291878843, "tokio", false, 2920671594485582797], [5986029879202738730, "log", false, 16162741913367429548], [7414427314941361239, "hyper", false, 4913518317987652970], [7620660491849607393, "futures_core", false, 9359995650856963640], [8915503303801890683, "http_body", false, 5072299136653256730], [9689903380558560274, "serde", false, 106362935358965852], [10229185211513642314, "mime", false, 3404994555163973800], [10629569228670356391, "futures_util", false, 15477738578482463613], [11107720164717273507, "system_configuration", false, 13992289621990120815], [12186126227181294540, "tokio_native_tls", false, 13788060465479053067], [12367227501898450486, "hyper_tls", false, 5224012869695775620], [13809605890706463735, "h2", false, 1214696367313876389], [14564311161534545801, "encoding_rs", false, 5809495112898744378], [15367738274754116744, "serde_json", false, 5336973750309629001], [16066129441945555748, "bytes", false, 16328044339417842794], [16311359161338405624, "rustls_pemfile", false, 7490523484830177171], [16542808166767769916, "serde_urlencoded", false, 1990779879862071648], [16785601910559813697, "native_tls_crate", false, 2962908149875551809], [18066890886671768183, "base64", false, 12486579130706063238]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/reqwest-38f81692cb09e764/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}