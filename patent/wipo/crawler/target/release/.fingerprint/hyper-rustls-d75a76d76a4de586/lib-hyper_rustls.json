{"rustc": 5357548097637079788, "features": "[\"http1\", \"http2\", \"native-tokio\", \"rustls-native-certs\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 2040997289075261528, "path": 1988268487525428912, "deps": [[778154619793643451, "hyper_util", false, 1713642418152933927], [784494742817713399, "tower_service", false, 17704212177869149945], [4613442558495818734, "rustls_native_certs", false, 5325989948300591363], [5138218615291878843, "tokio", false, 2920671594485582797], [8880649100394045925, "rustls", false, 1128401762955858859], [9010263965687315507, "http", false, 5237268969210199375], [11895591994124935963, "tokio_rustls", false, 8168218250413929723], [11957360342995674422, "hyper", false, 14997367858390719353], [16009134724182327169, "pki_types", false, 16639563445985851306]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/hyper-rustls-d75a76d76a4de586/dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}