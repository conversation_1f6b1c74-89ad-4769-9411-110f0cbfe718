{"rustc": 5357548097637079788, "features": "[\"aws-lc-sys\", \"prebuilt-nasm\"]", "declared_features": "[\"alloc\", \"asan\", \"aws-lc-sys\", \"bindgen\", \"default\", \"fips\", \"non-fips\", \"prebuilt-nasm\", \"ring-io\", \"ring-sig-verify\", \"test_logging\", \"unstable\"]", "target": 18300691495230371829, "profile": 2040997289075261528, "path": 6789247798651936332, "deps": [[6528079939221783635, "zeroize", false, 1449833509155880236], [16646688678199661021, "aws_lc_sys", false, 10706129634260609125], [16944451698427853066, "build_script_build", false, 15232842565282148365]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/aws-lc-rs-9f11ae820ae0e6c0/dep-lib-aws_lc_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}