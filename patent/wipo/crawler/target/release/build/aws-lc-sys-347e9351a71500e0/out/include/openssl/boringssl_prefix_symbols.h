// Copyright (c) 2018, Google Inc.
//
// Permission to use, copy, modify, and/or distribute this software for any
// purpose with or without fee is hereby granted, provided that the above
// copyright notice and this permission notice appear in all copies.
//
// THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
// OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
// CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

#ifndef BORINGSSL_PREFIX_SYMBOLS_H

#define BORINGSSL_PREFIX_SYMBOLS_H	

#ifndef BORINGSSL_PREFIX
#define BORINGSSL_PREFIX aws_lc_0_29_0
#endif // BORINGSSL_PREFIX


// BORINGSSL_ADD_PREFIX pastes two identifiers into one. It performs one
// iteration of macro expansion on its arguments before pasting.
#define BORINGSSL_ADD_PREFIX(a, b) BORINGSSL_ADD_PREFIX_INNER(a, b)
#define BORINGSSL_ADD_PREFIX_INNER(a, b) a ## _ ## b

#define ACCESS_DESCRIPTION_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ACCESS_DESCRIPTION_free)
#define ACCESS_DESCRIPTION_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ACCESS_DESCRIPTION_it)
#define ACCESS_DESCRIPTION_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ACCESS_DESCRIPTION_new)
#define AES_CMAC BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_CMAC)
#define AES_cbc_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_cbc_encrypt)
#define AES_cfb128_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_cfb128_encrypt)
#define AES_cfb1_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_cfb1_encrypt)
#define AES_cfb8_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_cfb8_encrypt)
#define AES_ctr128_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_ctr128_encrypt)
#define AES_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_decrypt)
#define AES_ecb_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_ecb_encrypt)
#define AES_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_encrypt)
#define AES_ofb128_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_ofb128_encrypt)
#define AES_set_decrypt_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_set_decrypt_key)
#define AES_set_encrypt_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_set_encrypt_key)
#define AES_unwrap_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_unwrap_key)
#define AES_unwrap_key_padded BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_unwrap_key_padded)
#define AES_wrap_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_wrap_key)
#define AES_wrap_key_padded BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AES_wrap_key_padded)
#define ASN1_ANY_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ANY_it)
#define ASN1_BIT_STRING_check BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BIT_STRING_check)
#define ASN1_BIT_STRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BIT_STRING_free)
#define ASN1_BIT_STRING_get_bit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BIT_STRING_get_bit)
#define ASN1_BIT_STRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BIT_STRING_it)
#define ASN1_BIT_STRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BIT_STRING_new)
#define ASN1_BIT_STRING_num_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BIT_STRING_num_bytes)
#define ASN1_BIT_STRING_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BIT_STRING_set)
#define ASN1_BIT_STRING_set_bit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BIT_STRING_set_bit)
#define ASN1_BMPSTRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BMPSTRING_free)
#define ASN1_BMPSTRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BMPSTRING_it)
#define ASN1_BMPSTRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BMPSTRING_new)
#define ASN1_BOOLEAN_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_BOOLEAN_it)
#define ASN1_ENUMERATED_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ENUMERATED_free)
#define ASN1_ENUMERATED_get BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ENUMERATED_get)
#define ASN1_ENUMERATED_get_int64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ENUMERATED_get_int64)
#define ASN1_ENUMERATED_get_uint64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ENUMERATED_get_uint64)
#define ASN1_ENUMERATED_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ENUMERATED_it)
#define ASN1_ENUMERATED_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ENUMERATED_new)
#define ASN1_ENUMERATED_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ENUMERATED_set)
#define ASN1_ENUMERATED_set_int64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ENUMERATED_set_int64)
#define ASN1_ENUMERATED_set_uint64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ENUMERATED_set_uint64)
#define ASN1_ENUMERATED_to_BN BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_ENUMERATED_to_BN)
#define ASN1_FBOOLEAN_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_FBOOLEAN_it)
#define ASN1_GENERALIZEDTIME_adj BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALIZEDTIME_adj)
#define ASN1_GENERALIZEDTIME_check BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALIZEDTIME_check)
#define ASN1_GENERALIZEDTIME_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALIZEDTIME_free)
#define ASN1_GENERALIZEDTIME_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALIZEDTIME_it)
#define ASN1_GENERALIZEDTIME_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALIZEDTIME_new)
#define ASN1_GENERALIZEDTIME_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALIZEDTIME_print)
#define ASN1_GENERALIZEDTIME_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALIZEDTIME_set)
#define ASN1_GENERALIZEDTIME_set_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALIZEDTIME_set_string)
#define ASN1_GENERALSTRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALSTRING_free)
#define ASN1_GENERALSTRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALSTRING_it)
#define ASN1_GENERALSTRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_GENERALSTRING_new)
#define ASN1_IA5STRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_IA5STRING_free)
#define ASN1_IA5STRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_IA5STRING_it)
#define ASN1_IA5STRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_IA5STRING_new)
#define ASN1_INTEGER_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_cmp)
#define ASN1_INTEGER_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_dup)
#define ASN1_INTEGER_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_free)
#define ASN1_INTEGER_get BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_get)
#define ASN1_INTEGER_get_int64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_get_int64)
#define ASN1_INTEGER_get_uint64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_get_uint64)
#define ASN1_INTEGER_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_it)
#define ASN1_INTEGER_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_new)
#define ASN1_INTEGER_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_set)
#define ASN1_INTEGER_set_int64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_set_int64)
#define ASN1_INTEGER_set_uint64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_set_uint64)
#define ASN1_INTEGER_to_BN BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_INTEGER_to_BN)
#define ASN1_NULL_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_NULL_free)
#define ASN1_NULL_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_NULL_it)
#define ASN1_NULL_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_NULL_new)
#define ASN1_OBJECT_create BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_OBJECT_create)
#define ASN1_OBJECT_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_OBJECT_free)
#define ASN1_OBJECT_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_OBJECT_it)
#define ASN1_OBJECT_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_OBJECT_new)
#define ASN1_OCTET_STRING_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_OCTET_STRING_cmp)
#define ASN1_OCTET_STRING_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_OCTET_STRING_dup)
#define ASN1_OCTET_STRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_OCTET_STRING_free)
#define ASN1_OCTET_STRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_OCTET_STRING_it)
#define ASN1_OCTET_STRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_OCTET_STRING_new)
#define ASN1_OCTET_STRING_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_OCTET_STRING_set)
#define ASN1_PRINTABLESTRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_PRINTABLESTRING_free)
#define ASN1_PRINTABLESTRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_PRINTABLESTRING_it)
#define ASN1_PRINTABLESTRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_PRINTABLESTRING_new)
#define ASN1_PRINTABLE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_PRINTABLE_free)
#define ASN1_PRINTABLE_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_PRINTABLE_it)
#define ASN1_PRINTABLE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_PRINTABLE_new)
#define ASN1_SEQUENCE_ANY_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_SEQUENCE_ANY_it)
#define ASN1_SEQUENCE_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_SEQUENCE_it)
#define ASN1_SET_ANY_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_SET_ANY_it)
#define ASN1_STRING_TABLE_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_TABLE_add)
#define ASN1_STRING_TABLE_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_TABLE_cleanup)
#define ASN1_STRING_clear_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_clear_free)
#define ASN1_STRING_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_cmp)
#define ASN1_STRING_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_copy)
#define ASN1_STRING_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_data)
#define ASN1_STRING_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_dup)
#define ASN1_STRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_free)
#define ASN1_STRING_get0_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_get0_data)
#define ASN1_STRING_get_default_mask BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_get_default_mask)
#define ASN1_STRING_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_length)
#define ASN1_STRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_new)
#define ASN1_STRING_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_print)
#define ASN1_STRING_print_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_print_ex)
#define ASN1_STRING_print_ex_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_print_ex_fp)
#define ASN1_STRING_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_set)
#define ASN1_STRING_set0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_set0)
#define ASN1_STRING_set_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_set_by_NID)
#define ASN1_STRING_set_default_mask BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_set_default_mask)
#define ASN1_STRING_set_default_mask_asc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_set_default_mask_asc)
#define ASN1_STRING_to_UTF8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_to_UTF8)
#define ASN1_STRING_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_type)
#define ASN1_STRING_type_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_STRING_type_new)
#define ASN1_T61STRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_T61STRING_free)
#define ASN1_T61STRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_T61STRING_it)
#define ASN1_T61STRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_T61STRING_new)
#define ASN1_TBOOLEAN_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TBOOLEAN_it)
#define ASN1_TIME_adj BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_adj)
#define ASN1_TIME_check BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_check)
#define ASN1_TIME_diff BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_diff)
#define ASN1_TIME_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_free)
#define ASN1_TIME_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_it)
#define ASN1_TIME_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_new)
#define ASN1_TIME_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_print)
#define ASN1_TIME_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_set)
#define ASN1_TIME_set_posix BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_set_posix)
#define ASN1_TIME_set_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_set_string)
#define ASN1_TIME_set_string_X509 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_set_string_X509)
#define ASN1_TIME_to_generalizedtime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_to_generalizedtime)
#define ASN1_TIME_to_posix BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_to_posix)
#define ASN1_TIME_to_time_t BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_to_time_t)
#define ASN1_TIME_to_tm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TIME_to_tm)
#define ASN1_TYPE_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TYPE_cmp)
#define ASN1_TYPE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TYPE_free)
#define ASN1_TYPE_get BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TYPE_get)
#define ASN1_TYPE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TYPE_new)
#define ASN1_TYPE_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TYPE_set)
#define ASN1_TYPE_set1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_TYPE_set1)
#define ASN1_UNIVERSALSTRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UNIVERSALSTRING_free)
#define ASN1_UNIVERSALSTRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UNIVERSALSTRING_it)
#define ASN1_UNIVERSALSTRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UNIVERSALSTRING_new)
#define ASN1_UTCTIME_adj BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTCTIME_adj)
#define ASN1_UTCTIME_check BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTCTIME_check)
#define ASN1_UTCTIME_cmp_time_t BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTCTIME_cmp_time_t)
#define ASN1_UTCTIME_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTCTIME_free)
#define ASN1_UTCTIME_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTCTIME_it)
#define ASN1_UTCTIME_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTCTIME_new)
#define ASN1_UTCTIME_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTCTIME_print)
#define ASN1_UTCTIME_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTCTIME_set)
#define ASN1_UTCTIME_set_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTCTIME_set_string)
#define ASN1_UTF8STRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTF8STRING_free)
#define ASN1_UTF8STRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTF8STRING_it)
#define ASN1_UTF8STRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_UTF8STRING_new)
#define ASN1_VISIBLESTRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_VISIBLESTRING_free)
#define ASN1_VISIBLESTRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_VISIBLESTRING_it)
#define ASN1_VISIBLESTRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_VISIBLESTRING_new)
#define ASN1_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_digest)
#define ASN1_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_dup)
#define ASN1_generate_v3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_generate_v3)
#define ASN1_get_object BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_get_object)
#define ASN1_i2d_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_i2d_bio)
#define ASN1_item_d2i BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_d2i)
#define ASN1_item_d2i_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_d2i_bio)
#define ASN1_item_d2i_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_d2i_fp)
#define ASN1_item_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_digest)
#define ASN1_item_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_dup)
#define ASN1_item_ex_d2i BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_ex_d2i)
#define ASN1_item_ex_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_ex_free)
#define ASN1_item_ex_i2d BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_ex_i2d)
#define ASN1_item_ex_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_ex_new)
#define ASN1_item_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_free)
#define ASN1_item_i2d BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_i2d)
#define ASN1_item_i2d_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_i2d_bio)
#define ASN1_item_i2d_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_i2d_fp)
#define ASN1_item_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_new)
#define ASN1_item_pack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_pack)
#define ASN1_item_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_sign)
#define ASN1_item_sign_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_sign_ctx)
#define ASN1_item_unpack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_unpack)
#define ASN1_item_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_item_verify)
#define ASN1_mbstring_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_mbstring_copy)
#define ASN1_mbstring_ncopy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_mbstring_ncopy)
#define ASN1_object_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_object_size)
#define ASN1_primitive_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_primitive_free)
#define ASN1_put_eoc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_put_eoc)
#define ASN1_put_object BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_put_object)
#define ASN1_tag2bit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_tag2bit)
#define ASN1_tag2str BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_tag2str)
#define ASN1_template_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ASN1_template_free)
#define AUTHORITY_INFO_ACCESS_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AUTHORITY_INFO_ACCESS_free)
#define AUTHORITY_INFO_ACCESS_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AUTHORITY_INFO_ACCESS_it)
#define AUTHORITY_INFO_ACCESS_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AUTHORITY_INFO_ACCESS_new)
#define AUTHORITY_KEYID_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AUTHORITY_KEYID_free)
#define AUTHORITY_KEYID_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AUTHORITY_KEYID_it)
#define AUTHORITY_KEYID_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AUTHORITY_KEYID_new)
#define AWSLC_non_fips_pkey_evp_asn1_methods BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AWSLC_non_fips_pkey_evp_asn1_methods)
#define AWSLC_non_fips_pkey_evp_methods BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AWSLC_non_fips_pkey_evp_methods)
#define AWSLC_thread_local_clear BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AWSLC_thread_local_clear)
#define AWSLC_thread_local_shutdown BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AWSLC_thread_local_shutdown)
#define AWS_LC_FIPS_failure BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, AWS_LC_FIPS_failure)
#define BASIC_CONSTRAINTS_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BASIC_CONSTRAINTS_free)
#define BASIC_CONSTRAINTS_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BASIC_CONSTRAINTS_it)
#define BASIC_CONSTRAINTS_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BASIC_CONSTRAINTS_new)
#define BF_cbc_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BF_cbc_encrypt)
#define BF_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BF_decrypt)
#define BF_ecb_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BF_ecb_encrypt)
#define BF_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BF_encrypt)
#define BF_set_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BF_set_key)
#define BIO_append_filename BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_append_filename)
#define BIO_callback_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_callback_ctrl)
#define BIO_clear_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_clear_flags)
#define BIO_clear_retry_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_clear_retry_flags)
#define BIO_copy_next_retry BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_copy_next_retry)
#define BIO_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_ctrl)
#define BIO_ctrl_get_read_request BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_ctrl_get_read_request)
#define BIO_ctrl_get_write_guarantee BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_ctrl_get_write_guarantee)
#define BIO_ctrl_pending BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_ctrl_pending)
#define BIO_destroy_bio_pair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_destroy_bio_pair)
#define BIO_do_connect BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_do_connect)
#define BIO_eof BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_eof)
#define BIO_f_base64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_f_base64)
#define BIO_f_cipher BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_f_cipher)
#define BIO_f_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_f_md)
#define BIO_find_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_find_type)
#define BIO_flush BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_flush)
#define BIO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_free)
#define BIO_free_all BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_free_all)
#define BIO_get_callback_arg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_callback_arg)
#define BIO_get_cipher_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_cipher_ctx)
#define BIO_get_cipher_status BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_cipher_status)
#define BIO_get_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_data)
#define BIO_get_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_ex_data)
#define BIO_get_ex_new_index BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_ex_new_index)
#define BIO_get_fd BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_fd)
#define BIO_get_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_fp)
#define BIO_get_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_init)
#define BIO_get_md_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_md_ctx)
#define BIO_get_mem_ptr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_mem_ptr)
#define BIO_get_new_index BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_new_index)
#define BIO_get_retry_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_retry_flags)
#define BIO_get_retry_reason BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_retry_reason)
#define BIO_get_shutdown BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_get_shutdown)
#define BIO_gets BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_gets)
#define BIO_hexdump BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_hexdump)
#define BIO_indent BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_indent)
#define BIO_int_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_int_ctrl)
#define BIO_mem_contents BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_mem_contents)
#define BIO_meth_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_free)
#define BIO_meth_get_callback_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_get_callback_ctrl)
#define BIO_meth_get_create BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_get_create)
#define BIO_meth_get_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_get_ctrl)
#define BIO_meth_get_destroy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_get_destroy)
#define BIO_meth_get_gets BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_get_gets)
#define BIO_meth_get_puts BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_get_puts)
#define BIO_meth_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_new)
#define BIO_meth_set_callback_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_set_callback_ctrl)
#define BIO_meth_set_create BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_set_create)
#define BIO_meth_set_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_set_ctrl)
#define BIO_meth_set_destroy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_set_destroy)
#define BIO_meth_set_gets BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_set_gets)
#define BIO_meth_set_puts BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_set_puts)
#define BIO_meth_set_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_set_read)
#define BIO_meth_set_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_meth_set_write)
#define BIO_method_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_method_name)
#define BIO_method_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_method_type)
#define BIO_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_new)
#define BIO_new_bio_pair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_new_bio_pair)
#define BIO_new_connect BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_new_connect)
#define BIO_new_fd BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_new_fd)
#define BIO_new_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_new_file)
#define BIO_new_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_new_fp)
#define BIO_new_mem_buf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_new_mem_buf)
#define BIO_new_socket BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_new_socket)
#define BIO_next BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_next)
#define BIO_number_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_number_read)
#define BIO_number_written BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_number_written)
#define BIO_pending BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_pending)
#define BIO_pop BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_pop)
#define BIO_printf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_printf)
#define BIO_ptr_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_ptr_ctrl)
#define BIO_push BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_push)
#define BIO_puts BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_puts)
#define BIO_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_read)
#define BIO_read_asn1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_read_asn1)
#define BIO_read_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_read_ex)
#define BIO_read_filename BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_read_filename)
#define BIO_reset BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_reset)
#define BIO_rw_filename BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_rw_filename)
#define BIO_s_connect BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_s_connect)
#define BIO_s_fd BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_s_fd)
#define BIO_s_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_s_file)
#define BIO_s_mem BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_s_mem)
#define BIO_s_secmem BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_s_secmem)
#define BIO_s_socket BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_s_socket)
#define BIO_seek BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_seek)
#define BIO_set_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_callback)
#define BIO_set_callback_arg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_callback_arg)
#define BIO_set_callback_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_callback_ex)
#define BIO_set_cipher BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_cipher)
#define BIO_set_close BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_close)
#define BIO_set_conn_hostname BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_conn_hostname)
#define BIO_set_conn_int_port BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_conn_int_port)
#define BIO_set_conn_port BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_conn_port)
#define BIO_set_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_data)
#define BIO_set_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_ex_data)
#define BIO_set_fd BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_fd)
#define BIO_set_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_flags)
#define BIO_set_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_fp)
#define BIO_set_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_init)
#define BIO_set_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_md)
#define BIO_set_mem_buf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_mem_buf)
#define BIO_set_mem_eof_return BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_mem_eof_return)
#define BIO_set_nbio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_nbio)
#define BIO_set_retry_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_retry_read)
#define BIO_set_retry_reason BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_retry_reason)
#define BIO_set_retry_special BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_retry_special)
#define BIO_set_retry_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_retry_write)
#define BIO_set_shutdown BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_shutdown)
#define BIO_set_write_buffer_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_set_write_buffer_size)
#define BIO_should_io_special BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_should_io_special)
#define BIO_should_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_should_read)
#define BIO_should_retry BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_should_retry)
#define BIO_should_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_should_write)
#define BIO_shutdown_wr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_shutdown_wr)
#define BIO_snprintf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_snprintf)
#define BIO_tell BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_tell)
#define BIO_test_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_test_flags)
#define BIO_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_up_ref)
#define BIO_vfree BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_vfree)
#define BIO_vsnprintf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_vsnprintf)
#define BIO_wpending BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_wpending)
#define BIO_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_write)
#define BIO_write_all BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_write_all)
#define BIO_write_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_write_ex)
#define BIO_write_filename BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BIO_write_filename)
#define BLAKE2B256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BLAKE2B256)
#define BLAKE2B256_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BLAKE2B256_Final)
#define BLAKE2B256_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BLAKE2B256_Init)
#define BLAKE2B256_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BLAKE2B256_Update)
#define BN_BLINDING_convert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_BLINDING_convert)
#define BN_BLINDING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_BLINDING_free)
#define BN_BLINDING_invalidate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_BLINDING_invalidate)
#define BN_BLINDING_invert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_BLINDING_invert)
#define BN_BLINDING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_BLINDING_new)
#define BN_CTX_end BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_CTX_end)
#define BN_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_CTX_free)
#define BN_CTX_get BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_CTX_get)
#define BN_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_CTX_new)
#define BN_CTX_secure_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_CTX_secure_new)
#define BN_CTX_start BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_CTX_start)
#define BN_GENCB_call BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_GENCB_call)
#define BN_GENCB_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_GENCB_free)
#define BN_GENCB_get_arg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_GENCB_get_arg)
#define BN_GENCB_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_GENCB_new)
#define BN_GENCB_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_GENCB_set)
#define BN_GENCB_set_old BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_GENCB_set_old)
#define BN_MONT_CTX_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_MONT_CTX_copy)
#define BN_MONT_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_MONT_CTX_free)
#define BN_MONT_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_MONT_CTX_new)
#define BN_MONT_CTX_new_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_MONT_CTX_new_consttime)
#define BN_MONT_CTX_new_for_modulus BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_MONT_CTX_new_for_modulus)
#define BN_MONT_CTX_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_MONT_CTX_set)
#define BN_MONT_CTX_set_locked BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_MONT_CTX_set_locked)
#define BN_abs_is_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_abs_is_word)
#define BN_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_add)
#define BN_add_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_add_word)
#define BN_asc2bn BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_asc2bn)
#define BN_bin2bn BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_bin2bn)
#define BN_bn2bin BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_bn2bin)
#define BN_bn2bin_padded BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_bn2bin_padded)
#define BN_bn2binpad BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_bn2binpad)
#define BN_bn2cbb_padded BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_bn2cbb_padded)
#define BN_bn2dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_bn2dec)
#define BN_bn2hex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_bn2hex)
#define BN_bn2le_padded BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_bn2le_padded)
#define BN_bn2mpi BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_bn2mpi)
#define BN_clear BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_clear)
#define BN_clear_bit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_clear_bit)
#define BN_clear_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_clear_free)
#define BN_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_cmp)
#define BN_cmp_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_cmp_word)
#define BN_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_copy)
#define BN_count_low_zero_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_count_low_zero_bits)
#define BN_dec2bn BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_dec2bn)
#define BN_div BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_div)
#define BN_div_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_div_word)
#define BN_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_dup)
#define BN_enhanced_miller_rabin_primality_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_enhanced_miller_rabin_primality_test)
#define BN_equal_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_equal_consttime)
#define BN_exp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_exp)
#define BN_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_free)
#define BN_from_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_from_montgomery)
#define BN_gcd BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_gcd)
#define BN_generate_prime_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_generate_prime_ex)
#define BN_get_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_get_flags)
#define BN_get_minimal_width BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_get_minimal_width)
#define BN_get_rfc3526_prime_1536 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_get_rfc3526_prime_1536)
#define BN_get_rfc3526_prime_2048 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_get_rfc3526_prime_2048)
#define BN_get_rfc3526_prime_3072 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_get_rfc3526_prime_3072)
#define BN_get_rfc3526_prime_4096 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_get_rfc3526_prime_4096)
#define BN_get_rfc3526_prime_6144 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_get_rfc3526_prime_6144)
#define BN_get_rfc3526_prime_8192 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_get_rfc3526_prime_8192)
#define BN_get_u64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_get_u64)
#define BN_get_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_get_word)
#define BN_hex2bn BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_hex2bn)
#define BN_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_init)
#define BN_is_bit_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_is_bit_set)
#define BN_is_negative BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_is_negative)
#define BN_is_odd BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_is_odd)
#define BN_is_one BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_is_one)
#define BN_is_pow2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_is_pow2)
#define BN_is_prime_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_is_prime_ex)
#define BN_is_prime_fasttest_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_is_prime_fasttest_ex)
#define BN_is_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_is_word)
#define BN_is_zero BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_is_zero)
#define BN_le2bn BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_le2bn)
#define BN_lshift BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_lshift)
#define BN_lshift1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_lshift1)
#define BN_marshal_asn1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_marshal_asn1)
#define BN_mask_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mask_bits)
#define BN_mod_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_add)
#define BN_mod_add_quick BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_add_quick)
#define BN_mod_exp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_exp)
#define BN_mod_exp2_mont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_exp2_mont)
#define BN_mod_exp_mont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_exp_mont)
#define BN_mod_exp_mont_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_exp_mont_consttime)
#define BN_mod_exp_mont_consttime_x2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_exp_mont_consttime_x2)
#define BN_mod_exp_mont_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_exp_mont_word)
#define BN_mod_inverse BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_inverse)
#define BN_mod_inverse_blinded BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_inverse_blinded)
#define BN_mod_inverse_odd BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_inverse_odd)
#define BN_mod_lshift BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_lshift)
#define BN_mod_lshift1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_lshift1)
#define BN_mod_lshift1_quick BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_lshift1_quick)
#define BN_mod_lshift_quick BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_lshift_quick)
#define BN_mod_mul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_mul)
#define BN_mod_mul_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_mul_montgomery)
#define BN_mod_pow2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_pow2)
#define BN_mod_sqr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_sqr)
#define BN_mod_sqrt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_sqrt)
#define BN_mod_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_sub)
#define BN_mod_sub_quick BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_sub_quick)
#define BN_mod_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mod_word)
#define BN_mpi2bn BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mpi2bn)
#define BN_mul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mul)
#define BN_mul_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_mul_word)
#define BN_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_new)
#define BN_nnmod BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_nnmod)
#define BN_nnmod_pow2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_nnmod_pow2)
#define BN_num_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_num_bits)
#define BN_num_bits_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_num_bits_word)
#define BN_num_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_num_bytes)
#define BN_one BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_one)
#define BN_parse_asn1_unsigned BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_parse_asn1_unsigned)
#define BN_primality_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_primality_test)
#define BN_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_print)
#define BN_print_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_print_fp)
#define BN_pseudo_rand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_pseudo_rand)
#define BN_pseudo_rand_range BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_pseudo_rand_range)
#define BN_rand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_rand)
#define BN_rand_range BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_rand_range)
#define BN_rand_range_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_rand_range_ex)
#define BN_rshift BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_rshift)
#define BN_rshift1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_rshift1)
#define BN_secure_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_secure_new)
#define BN_set_bit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_set_bit)
#define BN_set_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_set_flags)
#define BN_set_negative BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_set_negative)
#define BN_set_u64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_set_u64)
#define BN_set_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_set_word)
#define BN_sqr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_sqr)
#define BN_sqrt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_sqrt)
#define BN_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_sub)
#define BN_sub_word BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_sub_word)
#define BN_to_ASN1_ENUMERATED BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_to_ASN1_ENUMERATED)
#define BN_to_ASN1_INTEGER BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_to_ASN1_INTEGER)
#define BN_to_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_to_montgomery)
#define BN_uadd BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_uadd)
#define BN_ucmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_ucmp)
#define BN_usub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_usub)
#define BN_value_one BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_value_one)
#define BN_zero BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BN_zero)
#define BORINGSSL_function_hit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BORINGSSL_function_hit)
#define BORINGSSL_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BORINGSSL_self_test)
#define BUF_MEM_append BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_MEM_append)
#define BUF_MEM_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_MEM_free)
#define BUF_MEM_grow BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_MEM_grow)
#define BUF_MEM_grow_clean BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_MEM_grow_clean)
#define BUF_MEM_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_MEM_new)
#define BUF_MEM_reserve BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_MEM_reserve)
#define BUF_memdup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_memdup)
#define BUF_strdup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_strdup)
#define BUF_strlcat BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_strlcat)
#define BUF_strlcpy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_strlcpy)
#define BUF_strndup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_strndup)
#define BUF_strnlen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, BUF_strnlen)
#define CAST_S_table0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_S_table0)
#define CAST_S_table1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_S_table1)
#define CAST_S_table2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_S_table2)
#define CAST_S_table3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_S_table3)
#define CAST_S_table4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_S_table4)
#define CAST_S_table5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_S_table5)
#define CAST_S_table6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_S_table6)
#define CAST_S_table7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_S_table7)
#define CAST_cbc_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_cbc_encrypt)
#define CAST_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_decrypt)
#define CAST_ecb_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_ecb_encrypt)
#define CAST_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_encrypt)
#define CAST_set_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CAST_set_key)
#define CBB_add_asn1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_asn1)
#define CBB_add_asn1_bool BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_asn1_bool)
#define CBB_add_asn1_int64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_asn1_int64)
#define CBB_add_asn1_int64_with_tag BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_asn1_int64_with_tag)
#define CBB_add_asn1_octet_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_asn1_octet_string)
#define CBB_add_asn1_oid_from_text BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_asn1_oid_from_text)
#define CBB_add_asn1_uint64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_asn1_uint64)
#define CBB_add_asn1_uint64_with_tag BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_asn1_uint64_with_tag)
#define CBB_add_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_bytes)
#define CBB_add_space BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_space)
#define CBB_add_u16 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u16)
#define CBB_add_u16_length_prefixed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u16_length_prefixed)
#define CBB_add_u16le BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u16le)
#define CBB_add_u24 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u24)
#define CBB_add_u24_length_prefixed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u24_length_prefixed)
#define CBB_add_u32 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u32)
#define CBB_add_u32le BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u32le)
#define CBB_add_u64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u64)
#define CBB_add_u64le BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u64le)
#define CBB_add_u8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u8)
#define CBB_add_u8_length_prefixed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_u8_length_prefixed)
#define CBB_add_zeros BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_add_zeros)
#define CBB_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_cleanup)
#define CBB_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_data)
#define CBB_did_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_did_write)
#define CBB_discard_child BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_discard_child)
#define CBB_finish BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_finish)
#define CBB_finish_i2d BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_finish_i2d)
#define CBB_flush BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_flush)
#define CBB_flush_asn1_set_of BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_flush_asn1_set_of)
#define CBB_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_init)
#define CBB_init_fixed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_init_fixed)
#define CBB_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_len)
#define CBB_reserve BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_reserve)
#define CBB_zero BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBB_zero)
#define CBS_asn1_ber_to_der BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_asn1_ber_to_der)
#define CBS_asn1_bitstring_has_bit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_asn1_bitstring_has_bit)
#define CBS_asn1_oid_to_text BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_asn1_oid_to_text)
#define CBS_contains_zero_byte BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_contains_zero_byte)
#define CBS_copy_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_copy_bytes)
#define CBS_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_data)
#define CBS_get_any_asn1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_any_asn1)
#define CBS_get_any_asn1_element BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_any_asn1_element)
#define CBS_get_any_ber_asn1_element BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_any_ber_asn1_element)
#define CBS_get_asn1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_asn1)
#define CBS_get_asn1_bool BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_asn1_bool)
#define CBS_get_asn1_element BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_asn1_element)
#define CBS_get_asn1_implicit_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_asn1_implicit_string)
#define CBS_get_asn1_int64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_asn1_int64)
#define CBS_get_asn1_uint64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_asn1_uint64)
#define CBS_get_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_bytes)
#define CBS_get_last_u8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_last_u8)
#define CBS_get_optional_asn1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_optional_asn1)
#define CBS_get_optional_asn1_bool BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_optional_asn1_bool)
#define CBS_get_optional_asn1_int64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_optional_asn1_int64)
#define CBS_get_optional_asn1_octet_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_optional_asn1_octet_string)
#define CBS_get_optional_asn1_uint64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_optional_asn1_uint64)
#define CBS_get_u16 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u16)
#define CBS_get_u16_length_prefixed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u16_length_prefixed)
#define CBS_get_u16le BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u16le)
#define CBS_get_u24 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u24)
#define CBS_get_u24_length_prefixed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u24_length_prefixed)
#define CBS_get_u32 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u32)
#define CBS_get_u32le BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u32le)
#define CBS_get_u64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u64)
#define CBS_get_u64_decimal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u64_decimal)
#define CBS_get_u64le BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u64le)
#define CBS_get_u8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u8)
#define CBS_get_u8_length_prefixed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_u8_length_prefixed)
#define CBS_get_until_first BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_get_until_first)
#define CBS_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_init)
#define CBS_is_unsigned_asn1_integer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_is_unsigned_asn1_integer)
#define CBS_is_valid_asn1_bitstring BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_is_valid_asn1_bitstring)
#define CBS_is_valid_asn1_integer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_is_valid_asn1_integer)
#define CBS_is_valid_asn1_oid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_is_valid_asn1_oid)
#define CBS_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_len)
#define CBS_mem_equal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_mem_equal)
#define CBS_parse_generalized_time BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_parse_generalized_time)
#define CBS_parse_utc_time BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_parse_utc_time)
#define CBS_peek_asn1_tag BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_peek_asn1_tag)
#define CBS_skip BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_skip)
#define CBS_stow BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_stow)
#define CBS_strdup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CBS_strdup)
#define CERTIFICATEPOLICIES_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CERTIFICATEPOLICIES_free)
#define CERTIFICATEPOLICIES_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CERTIFICATEPOLICIES_it)
#define CERTIFICATEPOLICIES_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CERTIFICATEPOLICIES_new)
#define CMAC_CTX_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CMAC_CTX_copy)
#define CMAC_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CMAC_CTX_free)
#define CMAC_CTX_get0_cipher_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CMAC_CTX_get0_cipher_ctx)
#define CMAC_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CMAC_CTX_new)
#define CMAC_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CMAC_Final)
#define CMAC_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CMAC_Init)
#define CMAC_Reset BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CMAC_Reset)
#define CMAC_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CMAC_Update)
#define CONF_VALUE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CONF_VALUE_new)
#define CONF_get1_default_config_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CONF_get1_default_config_file)
#define CONF_modules_finish BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CONF_modules_finish)
#define CONF_modules_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CONF_modules_free)
#define CONF_modules_load_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CONF_modules_load_file)
#define CONF_modules_unload BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CONF_modules_unload)
#define CONF_parse_list BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CONF_parse_list)
#define CRL_DIST_POINTS_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRL_DIST_POINTS_free)
#define CRL_DIST_POINTS_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRL_DIST_POINTS_it)
#define CRL_DIST_POINTS_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRL_DIST_POINTS_new)
#define CRYPTO_BUFFER_POOL_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_POOL_free)
#define CRYPTO_BUFFER_POOL_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_POOL_new)
#define CRYPTO_BUFFER_alloc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_alloc)
#define CRYPTO_BUFFER_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_data)
#define CRYPTO_BUFFER_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_free)
#define CRYPTO_BUFFER_init_CBS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_init_CBS)
#define CRYPTO_BUFFER_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_len)
#define CRYPTO_BUFFER_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_new)
#define CRYPTO_BUFFER_new_from_CBS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_new_from_CBS)
#define CRYPTO_BUFFER_new_from_static_data_unsafe BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_new_from_static_data_unsafe)
#define CRYPTO_BUFFER_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_BUFFER_up_ref)
#define CRYPTO_MUTEX_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_MUTEX_cleanup)
#define CRYPTO_MUTEX_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_MUTEX_init)
#define CRYPTO_MUTEX_lock_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_MUTEX_lock_read)
#define CRYPTO_MUTEX_lock_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_MUTEX_lock_write)
#define CRYPTO_MUTEX_unlock_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_MUTEX_unlock_read)
#define CRYPTO_MUTEX_unlock_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_MUTEX_unlock_write)
#define CRYPTO_POLYVAL_finish BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_POLYVAL_finish)
#define CRYPTO_POLYVAL_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_POLYVAL_init)
#define CRYPTO_POLYVAL_update_blocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_POLYVAL_update_blocks)
#define CRYPTO_STATIC_MUTEX_lock_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_STATIC_MUTEX_lock_read)
#define CRYPTO_STATIC_MUTEX_lock_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_STATIC_MUTEX_lock_write)
#define CRYPTO_STATIC_MUTEX_unlock_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_STATIC_MUTEX_unlock_read)
#define CRYPTO_STATIC_MUTEX_unlock_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_STATIC_MUTEX_unlock_write)
#define CRYPTO_THREADID_current BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_THREADID_current)
#define CRYPTO_THREADID_set_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_THREADID_set_callback)
#define CRYPTO_THREADID_set_numeric BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_THREADID_set_numeric)
#define CRYPTO_THREADID_set_pointer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_THREADID_set_pointer)
#define CRYPTO_cbc128_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_cbc128_decrypt)
#define CRYPTO_cbc128_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_cbc128_encrypt)
#define CRYPTO_cfb128_1_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_cfb128_1_encrypt)
#define CRYPTO_cfb128_8_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_cfb128_8_encrypt)
#define CRYPTO_cfb128_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_cfb128_encrypt)
#define CRYPTO_chacha_20 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_chacha_20)
#define CRYPTO_cleanup_all_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_cleanup_all_ex_data)
#define CRYPTO_ctr128_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_ctr128_encrypt)
#define CRYPTO_ctr128_encrypt_ctr32 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_ctr128_encrypt_ctr32)
#define CRYPTO_fork_detect_ignore_madv_wipeonfork_for_testing BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_fork_detect_ignore_madv_wipeonfork_for_testing)
#define CRYPTO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_free)
#define CRYPTO_free_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_free_ex_data)
#define CRYPTO_gcm128_aad BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_gcm128_aad)
#define CRYPTO_gcm128_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_gcm128_decrypt)
#define CRYPTO_gcm128_decrypt_ctr32 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_gcm128_decrypt_ctr32)
#define CRYPTO_gcm128_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_gcm128_encrypt)
#define CRYPTO_gcm128_encrypt_ctr32 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_gcm128_encrypt_ctr32)
#define CRYPTO_gcm128_finish BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_gcm128_finish)
#define CRYPTO_gcm128_init_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_gcm128_init_key)
#define CRYPTO_gcm128_setiv BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_gcm128_setiv)
#define CRYPTO_gcm128_tag BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_gcm128_tag)
#define CRYPTO_get_dynlock_create_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_dynlock_create_callback)
#define CRYPTO_get_dynlock_destroy_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_dynlock_destroy_callback)
#define CRYPTO_get_dynlock_lock_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_dynlock_lock_callback)
#define CRYPTO_get_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_ex_data)
#define CRYPTO_get_ex_new_index BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_ex_new_index)
#define CRYPTO_get_fork_generation BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_fork_generation)
#define CRYPTO_get_lock_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_lock_name)
#define CRYPTO_get_locking_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_locking_callback)
#define CRYPTO_get_snapsafe_active BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_snapsafe_active)
#define CRYPTO_get_snapsafe_generation BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_snapsafe_generation)
#define CRYPTO_get_snapsafe_supported BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_snapsafe_supported)
#define CRYPTO_get_sysgenid_path BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_sysgenid_path)
#define CRYPTO_get_thread_local BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_get_thread_local)
#define CRYPTO_ghash_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_ghash_init)
#define CRYPTO_has_asm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_has_asm)
#define CRYPTO_has_broken_NEON BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_has_broken_NEON)
#define CRYPTO_hchacha20 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_hchacha20)
#define CRYPTO_init_sysrand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_init_sysrand)
#define CRYPTO_is_ARMv8_DIT_capable_for_testing BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_is_ARMv8_DIT_capable_for_testing)
#define CRYPTO_is_PPC64LE_vcrypto_capable BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_is_PPC64LE_vcrypto_capable)
#define CRYPTO_is_confidential_build BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_is_confidential_build)
#define CRYPTO_library_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_library_init)
#define CRYPTO_malloc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_malloc)
#define CRYPTO_malloc_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_malloc_init)
#define CRYPTO_mem_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_mem_ctrl)
#define CRYPTO_memcmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_memcmp)
#define CRYPTO_needs_hwcap2_workaround BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_needs_hwcap2_workaround)
#define CRYPTO_new_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_new_ex_data)
#define CRYPTO_num_locks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_num_locks)
#define CRYPTO_ofb128_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_ofb128_encrypt)
#define CRYPTO_once BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_once)
#define CRYPTO_poly1305_finish BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_poly1305_finish)
#define CRYPTO_poly1305_finish_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_poly1305_finish_neon)
#define CRYPTO_poly1305_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_poly1305_init)
#define CRYPTO_poly1305_init_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_poly1305_init_neon)
#define CRYPTO_poly1305_update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_poly1305_update)
#define CRYPTO_poly1305_update_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_poly1305_update_neon)
#define CRYPTO_pre_sandbox_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_pre_sandbox_init)
#define CRYPTO_rdrand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_rdrand)
#define CRYPTO_rdrand_multiple8_buf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_rdrand_multiple8_buf)
#define CRYPTO_realloc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_realloc)
#define CRYPTO_refcount_dec_and_test_zero BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_refcount_dec_and_test_zero)
#define CRYPTO_refcount_inc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_refcount_inc)
#define CRYPTO_secure_malloc_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_secure_malloc_init)
#define CRYPTO_secure_malloc_initialized BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_secure_malloc_initialized)
#define CRYPTO_secure_used BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_secure_used)
#define CRYPTO_set_add_lock_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_set_add_lock_callback)
#define CRYPTO_set_dynlock_create_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_set_dynlock_create_callback)
#define CRYPTO_set_dynlock_destroy_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_set_dynlock_destroy_callback)
#define CRYPTO_set_dynlock_lock_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_set_dynlock_lock_callback)
#define CRYPTO_set_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_set_ex_data)
#define CRYPTO_set_id_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_set_id_callback)
#define CRYPTO_set_locking_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_set_locking_callback)
#define CRYPTO_set_mem_functions BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_set_mem_functions)
#define CRYPTO_set_thread_local BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_set_thread_local)
#define CRYPTO_sysrand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_sysrand)
#define CRYPTO_sysrand_for_seed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_sysrand_for_seed)
#define CRYPTO_sysrand_if_available BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_sysrand_if_available)
#define CRYPTO_tls1_prf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_tls1_prf)
#define CRYPTO_xts128_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CRYPTO_xts128_encrypt)
#define CTR_DRBG_clear BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CTR_DRBG_clear)
#define CTR_DRBG_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CTR_DRBG_free)
#define CTR_DRBG_generate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CTR_DRBG_generate)
#define CTR_DRBG_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CTR_DRBG_init)
#define CTR_DRBG_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CTR_DRBG_new)
#define CTR_DRBG_reseed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, CTR_DRBG_reseed)
#define ChaCha20_ctr32_avx2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ChaCha20_ctr32_avx2)
#define ChaCha20_ctr32_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ChaCha20_ctr32_neon)
#define ChaCha20_ctr32_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ChaCha20_ctr32_nohw)
#define ChaCha20_ctr32_ssse3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ChaCha20_ctr32_ssse3)
#define ChaCha20_ctr32_ssse3_4x BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ChaCha20_ctr32_ssse3_4x)
#define DES_decrypt3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_decrypt3)
#define DES_ecb3_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_ecb3_encrypt)
#define DES_ecb3_encrypt_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_ecb3_encrypt_ex)
#define DES_ecb_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_ecb_encrypt)
#define DES_ecb_encrypt_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_ecb_encrypt_ex)
#define DES_ede2_cbc_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_ede2_cbc_encrypt)
#define DES_ede3_cbc_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_ede3_cbc_encrypt)
#define DES_ede3_cbc_encrypt_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_ede3_cbc_encrypt_ex)
#define DES_encrypt3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_encrypt3)
#define DES_is_weak_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_is_weak_key)
#define DES_key_sched BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_key_sched)
#define DES_ncbc_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_ncbc_encrypt)
#define DES_ncbc_encrypt_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_ncbc_encrypt_ex)
#define DES_set_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_set_key)
#define DES_set_key_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_set_key_ex)
#define DES_set_key_unchecked BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_set_key_unchecked)
#define DES_set_odd_parity BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DES_set_odd_parity)
#define DH_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_bits)
#define DH_check BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_check)
#define DH_check_pub_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_check_pub_key)
#define DH_clear_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_clear_flags)
#define DH_compute_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_compute_key)
#define DH_compute_key_hashed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_compute_key_hashed)
#define DH_compute_key_padded BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_compute_key_padded)
#define DH_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_free)
#define DH_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_generate_key)
#define DH_generate_parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_generate_parameters)
#define DH_generate_parameters_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_generate_parameters_ex)
#define DH_get0_g BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get0_g)
#define DH_get0_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get0_key)
#define DH_get0_p BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get0_p)
#define DH_get0_pqg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get0_pqg)
#define DH_get0_priv_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get0_priv_key)
#define DH_get0_pub_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get0_pub_key)
#define DH_get0_q BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get0_q)
#define DH_get_2048_256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get_2048_256)
#define DH_get_rfc7919_2048 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get_rfc7919_2048)
#define DH_get_rfc7919_3072 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get_rfc7919_3072)
#define DH_get_rfc7919_4096 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get_rfc7919_4096)
#define DH_get_rfc7919_8192 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_get_rfc7919_8192)
#define DH_marshal_parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_marshal_parameters)
#define DH_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_new)
#define DH_new_by_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_new_by_nid)
#define DH_num_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_num_bits)
#define DH_parse_parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_parse_parameters)
#define DH_set0_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_set0_key)
#define DH_set0_pqg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_set0_pqg)
#define DH_set_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_set_length)
#define DH_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_size)
#define DH_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DH_up_ref)
#define DHparams_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DHparams_dup)
#define DIRECTORYSTRING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DIRECTORYSTRING_free)
#define DIRECTORYSTRING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DIRECTORYSTRING_it)
#define DIRECTORYSTRING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DIRECTORYSTRING_new)
#define DISPLAYTEXT_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DISPLAYTEXT_free)
#define DISPLAYTEXT_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DISPLAYTEXT_it)
#define DISPLAYTEXT_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DISPLAYTEXT_new)
#define DIST_POINT_NAME_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DIST_POINT_NAME_free)
#define DIST_POINT_NAME_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DIST_POINT_NAME_it)
#define DIST_POINT_NAME_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DIST_POINT_NAME_new)
#define DIST_POINT_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DIST_POINT_free)
#define DIST_POINT_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DIST_POINT_it)
#define DIST_POINT_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DIST_POINT_new)
#define DIST_POINT_set_dpname BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DIST_POINT_set_dpname)
#define DSA_SIG_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_SIG_free)
#define DSA_SIG_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_SIG_get0)
#define DSA_SIG_marshal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_SIG_marshal)
#define DSA_SIG_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_SIG_new)
#define DSA_SIG_parse BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_SIG_parse)
#define DSA_SIG_set0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_SIG_set0)
#define DSA_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_bits)
#define DSA_check_signature BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_check_signature)
#define DSA_do_check_signature BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_do_check_signature)
#define DSA_do_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_do_sign)
#define DSA_do_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_do_verify)
#define DSA_dup_DH BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_dup_DH)
#define DSA_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_free)
#define DSA_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_generate_key)
#define DSA_generate_parameters_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_generate_parameters_ex)
#define DSA_get0_g BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_get0_g)
#define DSA_get0_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_get0_key)
#define DSA_get0_p BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_get0_p)
#define DSA_get0_pqg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_get0_pqg)
#define DSA_get0_priv_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_get0_priv_key)
#define DSA_get0_pub_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_get0_pub_key)
#define DSA_get0_q BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_get0_q)
#define DSA_get_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_get_ex_data)
#define DSA_get_ex_new_index BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_get_ex_new_index)
#define DSA_marshal_parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_marshal_parameters)
#define DSA_marshal_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_marshal_private_key)
#define DSA_marshal_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_marshal_public_key)
#define DSA_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_new)
#define DSA_parse_parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_parse_parameters)
#define DSA_parse_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_parse_private_key)
#define DSA_parse_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_parse_public_key)
#define DSA_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_print)
#define DSA_print_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_print_fp)
#define DSA_set0_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_set0_key)
#define DSA_set0_pqg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_set0_pqg)
#define DSA_set_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_set_ex_data)
#define DSA_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_sign)
#define DSA_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_size)
#define DSA_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_up_ref)
#define DSA_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSA_verify)
#define DSAparams_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, DSAparams_dup)
#define ECDH_compute_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDH_compute_key)
#define ECDH_compute_key_fips BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDH_compute_key_fips)
#define ECDH_compute_shared_secret BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDH_compute_shared_secret)
#define ECDSA_SIG_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_free)
#define ECDSA_SIG_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_from_bytes)
#define ECDSA_SIG_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_get0)
#define ECDSA_SIG_get0_r BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_get0_r)
#define ECDSA_SIG_get0_s BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_get0_s)
#define ECDSA_SIG_marshal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_marshal)
#define ECDSA_SIG_max_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_max_len)
#define ECDSA_SIG_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_new)
#define ECDSA_SIG_parse BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_parse)
#define ECDSA_SIG_set0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_set0)
#define ECDSA_SIG_to_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_SIG_to_bytes)
#define ECDSA_do_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_do_sign)
#define ECDSA_do_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_do_verify)
#define ECDSA_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_sign)
#define ECDSA_sign_with_nonce_and_leak_private_key_for_testing BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_sign_with_nonce_and_leak_private_key_for_testing)
#define ECDSA_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_size)
#define ECDSA_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECDSA_verify)
#define ECPKParameters_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ECPKParameters_print)
#define EC_GFp_mont_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GFp_mont_method)
#define EC_GFp_nistp224_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GFp_nistp224_method)
#define EC_GFp_nistp256_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GFp_nistp256_method)
#define EC_GFp_nistp384_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GFp_nistp384_method)
#define EC_GFp_nistp521_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GFp_nistp521_method)
#define EC_GFp_nistz256_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GFp_nistz256_method)
#define EC_GROUP_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_cmp)
#define EC_GROUP_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_dup)
#define EC_GROUP_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_free)
#define EC_GROUP_get0_generator BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get0_generator)
#define EC_GROUP_get0_order BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get0_order)
#define EC_GROUP_get0_seed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get0_seed)
#define EC_GROUP_get_asn1_flag BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get_asn1_flag)
#define EC_GROUP_get_cofactor BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get_cofactor)
#define EC_GROUP_get_curve_GFp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get_curve_GFp)
#define EC_GROUP_get_curve_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get_curve_name)
#define EC_GROUP_get_degree BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get_degree)
#define EC_GROUP_get_order BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get_order)
#define EC_GROUP_get_point_conversion_form BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get_point_conversion_form)
#define EC_GROUP_get_seed_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_get_seed_len)
#define EC_GROUP_method_of BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_method_of)
#define EC_GROUP_new_by_curve_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_new_by_curve_name)
#define EC_GROUP_new_by_curve_name_mutable BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_new_by_curve_name_mutable)
#define EC_GROUP_new_curve_GFp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_new_curve_GFp)
#define EC_GROUP_order_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_order_bits)
#define EC_GROUP_set_asn1_flag BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_set_asn1_flag)
#define EC_GROUP_set_generator BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_set_generator)
#define EC_GROUP_set_point_conversion_form BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_set_point_conversion_form)
#define EC_GROUP_set_seed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_GROUP_set_seed)
#define EC_KEY_METHOD_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_METHOD_free)
#define EC_KEY_METHOD_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_METHOD_new)
#define EC_KEY_METHOD_set_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_METHOD_set_flags)
#define EC_KEY_METHOD_set_init_awslc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_METHOD_set_init_awslc)
#define EC_KEY_METHOD_set_sign_awslc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_METHOD_set_sign_awslc)
#define EC_KEY_OpenSSL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_OpenSSL)
#define EC_KEY_check_fips BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_check_fips)
#define EC_KEY_check_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_check_key)
#define EC_KEY_derive_from_secret BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_derive_from_secret)
#define EC_KEY_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_dup)
#define EC_KEY_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_free)
#define EC_KEY_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_generate_key)
#define EC_KEY_generate_key_fips BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_generate_key_fips)
#define EC_KEY_get0_group BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_get0_group)
#define EC_KEY_get0_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_get0_private_key)
#define EC_KEY_get0_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_get0_public_key)
#define EC_KEY_get_conv_form BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_get_conv_form)
#define EC_KEY_get_default_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_get_default_method)
#define EC_KEY_get_enc_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_get_enc_flags)
#define EC_KEY_get_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_get_ex_data)
#define EC_KEY_get_ex_new_index BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_get_ex_new_index)
#define EC_KEY_get_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_get_method)
#define EC_KEY_is_opaque BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_is_opaque)
#define EC_KEY_key2buf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_key2buf)
#define EC_KEY_marshal_curve_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_marshal_curve_name)
#define EC_KEY_marshal_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_marshal_private_key)
#define EC_KEY_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_new)
#define EC_KEY_new_by_curve_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_new_by_curve_name)
#define EC_KEY_new_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_new_method)
#define EC_KEY_parse_curve_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_parse_curve_name)
#define EC_KEY_parse_parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_parse_parameters)
#define EC_KEY_parse_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_parse_private_key)
#define EC_KEY_set_asn1_flag BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_set_asn1_flag)
#define EC_KEY_set_conv_form BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_set_conv_form)
#define EC_KEY_set_enc_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_set_enc_flags)
#define EC_KEY_set_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_set_ex_data)
#define EC_KEY_set_group BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_set_group)
#define EC_KEY_set_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_set_method)
#define EC_KEY_set_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_set_private_key)
#define EC_KEY_set_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_set_public_key)
#define EC_KEY_set_public_key_affine_coordinates BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_set_public_key_affine_coordinates)
#define EC_KEY_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_KEY_up_ref)
#define EC_METHOD_get_field_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_METHOD_get_field_type)
#define EC_POINT_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_add)
#define EC_POINT_bn2point BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_bn2point)
#define EC_POINT_clear_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_clear_free)
#define EC_POINT_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_cmp)
#define EC_POINT_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_copy)
#define EC_POINT_dbl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_dbl)
#define EC_POINT_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_dup)
#define EC_POINT_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_free)
#define EC_POINT_get_affine_coordinates BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_get_affine_coordinates)
#define EC_POINT_get_affine_coordinates_GFp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_get_affine_coordinates_GFp)
#define EC_POINT_invert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_invert)
#define EC_POINT_is_at_infinity BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_is_at_infinity)
#define EC_POINT_is_on_curve BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_is_on_curve)
#define EC_POINT_mul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_mul)
#define EC_POINT_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_new)
#define EC_POINT_oct2point BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_oct2point)
#define EC_POINT_point2bn BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_point2bn)
#define EC_POINT_point2cbb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_point2cbb)
#define EC_POINT_point2oct BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_point2oct)
#define EC_POINT_set_affine_coordinates BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_set_affine_coordinates)
#define EC_POINT_set_affine_coordinates_GFp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_set_affine_coordinates_GFp)
#define EC_POINT_set_compressed_coordinates_GFp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_set_compressed_coordinates_GFp)
#define EC_POINT_set_to_infinity BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_POINT_set_to_infinity)
#define EC_curve_nid2nist BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_curve_nid2nist)
#define EC_curve_nist2nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_curve_nist2nid)
#define EC_get_builtin_curves BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_get_builtin_curves)
#define EC_group_p224 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_group_p224)
#define EC_group_p256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_group_p256)
#define EC_group_p384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_group_p384)
#define EC_group_p521 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_group_p521)
#define EC_group_secp256k1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_group_secp256k1)
#define EC_hash_to_curve_p256_xmd_sha256_sswu BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_hash_to_curve_p256_xmd_sha256_sswu)
#define EC_hash_to_curve_p384_xmd_sha384_sswu BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EC_hash_to_curve_p384_xmd_sha384_sswu)
#define ED25519_check_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519_check_public_key)
#define ED25519_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519_keypair)
#define ED25519_keypair_from_seed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519_keypair_from_seed)
#define ED25519_keypair_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519_keypair_internal)
#define ED25519_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519_sign)
#define ED25519_sign_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519_sign_no_self_test)
#define ED25519_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519_verify)
#define ED25519_verify_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519_verify_no_self_test)
#define ED25519ctx_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ctx_sign)
#define ED25519ctx_sign_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ctx_sign_no_self_test)
#define ED25519ctx_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ctx_verify)
#define ED25519ctx_verify_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ctx_verify_no_self_test)
#define ED25519ph_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ph_sign)
#define ED25519ph_sign_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ph_sign_digest)
#define ED25519ph_sign_digest_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ph_sign_digest_no_self_test)
#define ED25519ph_sign_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ph_sign_no_self_test)
#define ED25519ph_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ph_verify)
#define ED25519ph_verify_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ph_verify_digest)
#define ED25519ph_verify_digest_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ph_verify_digest_no_self_test)
#define ED25519ph_verify_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ED25519ph_verify_no_self_test)
#define EDIPARTYNAME_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EDIPARTYNAME_free)
#define EDIPARTYNAME_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EDIPARTYNAME_it)
#define EDIPARTYNAME_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EDIPARTYNAME_new)
#define ENGINE_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ENGINE_cleanup)
#define ENGINE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ENGINE_free)
#define ENGINE_get_EC BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ENGINE_get_EC)
#define ENGINE_get_RSA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ENGINE_get_RSA)
#define ENGINE_load_builtin_engines BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ENGINE_load_builtin_engines)
#define ENGINE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ENGINE_new)
#define ENGINE_register_all_complete BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ENGINE_register_all_complete)
#define ENGINE_set_EC BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ENGINE_set_EC)
#define ENGINE_set_RSA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ENGINE_set_RSA)
#define ERR_SAVE_STATE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_SAVE_STATE_free)
#define ERR_add_error_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_add_error_data)
#define ERR_add_error_dataf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_add_error_dataf)
#define ERR_clear_error BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_clear_error)
#define ERR_clear_system_error BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_clear_system_error)
#define ERR_error_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_error_string)
#define ERR_error_string_n BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_error_string_n)
#define ERR_free_strings BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_free_strings)
#define ERR_func_error_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_func_error_string)
#define ERR_get_error BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_get_error)
#define ERR_get_error_line BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_get_error_line)
#define ERR_get_error_line_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_get_error_line_data)
#define ERR_get_next_error_library BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_get_next_error_library)
#define ERR_lib_error_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_lib_error_string)
#define ERR_load_BIO_strings BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_load_BIO_strings)
#define ERR_load_CRYPTO_strings BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_load_CRYPTO_strings)
#define ERR_load_ERR_strings BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_load_ERR_strings)
#define ERR_load_RAND_strings BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_load_RAND_strings)
#define ERR_load_crypto_strings BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_load_crypto_strings)
#define ERR_peek_error BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_peek_error)
#define ERR_peek_error_line BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_peek_error_line)
#define ERR_peek_error_line_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_peek_error_line_data)
#define ERR_peek_last_error BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_peek_last_error)
#define ERR_peek_last_error_line BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_peek_last_error_line)
#define ERR_peek_last_error_line_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_peek_last_error_line_data)
#define ERR_pop_to_mark BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_pop_to_mark)
#define ERR_print_errors BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_print_errors)
#define ERR_print_errors_cb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_print_errors_cb)
#define ERR_print_errors_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_print_errors_fp)
#define ERR_put_error BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_put_error)
#define ERR_reason_error_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_reason_error_string)
#define ERR_remove_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_remove_state)
#define ERR_remove_thread_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_remove_thread_state)
#define ERR_restore_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_restore_state)
#define ERR_save_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_save_state)
#define ERR_set_error_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_set_error_data)
#define ERR_set_mark BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ERR_set_mark)
#define EVP_AEAD_CTX_aead BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_aead)
#define EVP_AEAD_CTX_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_cleanup)
#define EVP_AEAD_CTX_deserialize_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_deserialize_state)
#define EVP_AEAD_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_free)
#define EVP_AEAD_CTX_get_aead_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_get_aead_id)
#define EVP_AEAD_CTX_get_iv BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_get_iv)
#define EVP_AEAD_CTX_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_init)
#define EVP_AEAD_CTX_init_with_direction BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_init_with_direction)
#define EVP_AEAD_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_new)
#define EVP_AEAD_CTX_open BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_open)
#define EVP_AEAD_CTX_open_gather BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_open_gather)
#define EVP_AEAD_CTX_seal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_seal)
#define EVP_AEAD_CTX_seal_scatter BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_seal_scatter)
#define EVP_AEAD_CTX_serialize_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_serialize_state)
#define EVP_AEAD_CTX_tag_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_tag_len)
#define EVP_AEAD_CTX_zero BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_CTX_zero)
#define EVP_AEAD_get_iv_from_ipv4_nanosecs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_get_iv_from_ipv4_nanosecs)
#define EVP_AEAD_key_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_key_length)
#define EVP_AEAD_max_overhead BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_max_overhead)
#define EVP_AEAD_max_tag_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_max_tag_len)
#define EVP_AEAD_nonce_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_AEAD_nonce_length)
#define EVP_BytesToKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_BytesToKey)
#define EVP_CIPHER_CTX_block_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_block_size)
#define EVP_CIPHER_CTX_cipher BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_cipher)
#define EVP_CIPHER_CTX_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_cleanup)
#define EVP_CIPHER_CTX_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_copy)
#define EVP_CIPHER_CTX_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_ctrl)
#define EVP_CIPHER_CTX_encrypting BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_encrypting)
#define EVP_CIPHER_CTX_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_flags)
#define EVP_CIPHER_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_free)
#define EVP_CIPHER_CTX_get_app_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_get_app_data)
#define EVP_CIPHER_CTX_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_init)
#define EVP_CIPHER_CTX_iv_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_iv_length)
#define EVP_CIPHER_CTX_key_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_key_length)
#define EVP_CIPHER_CTX_mode BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_mode)
#define EVP_CIPHER_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_new)
#define EVP_CIPHER_CTX_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_nid)
#define EVP_CIPHER_CTX_reset BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_reset)
#define EVP_CIPHER_CTX_set_app_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_set_app_data)
#define EVP_CIPHER_CTX_set_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_set_flags)
#define EVP_CIPHER_CTX_set_key_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_set_key_length)
#define EVP_CIPHER_CTX_set_padding BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_CTX_set_padding)
#define EVP_CIPHER_block_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_block_size)
#define EVP_CIPHER_do_all_sorted BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_do_all_sorted)
#define EVP_CIPHER_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_flags)
#define EVP_CIPHER_iv_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_iv_length)
#define EVP_CIPHER_key_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_key_length)
#define EVP_CIPHER_mode BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_mode)
#define EVP_CIPHER_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_name)
#define EVP_CIPHER_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CIPHER_nid)
#define EVP_Cipher BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_Cipher)
#define EVP_CipherFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CipherFinal)
#define EVP_CipherFinal_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CipherFinal_ex)
#define EVP_CipherInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CipherInit)
#define EVP_CipherInit_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CipherInit_ex)
#define EVP_CipherUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_CipherUpdate)
#define EVP_DecodeBase64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecodeBase64)
#define EVP_DecodeBlock BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecodeBlock)
#define EVP_DecodeFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecodeFinal)
#define EVP_DecodeInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecodeInit)
#define EVP_DecodeUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecodeUpdate)
#define EVP_DecodedLength BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecodedLength)
#define EVP_DecryptFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecryptFinal)
#define EVP_DecryptFinal_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecryptFinal_ex)
#define EVP_DecryptInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecryptInit)
#define EVP_DecryptInit_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecryptInit_ex)
#define EVP_DecryptUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DecryptUpdate)
#define EVP_Digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_Digest)
#define EVP_DigestFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestFinal)
#define EVP_DigestFinalXOF BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestFinalXOF)
#define EVP_DigestFinal_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestFinal_ex)
#define EVP_DigestInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestInit)
#define EVP_DigestInit_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestInit_ex)
#define EVP_DigestSign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestSign)
#define EVP_DigestSignFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestSignFinal)
#define EVP_DigestSignInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestSignInit)
#define EVP_DigestSignUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestSignUpdate)
#define EVP_DigestSqueeze BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestSqueeze)
#define EVP_DigestUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestUpdate)
#define EVP_DigestVerify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestVerify)
#define EVP_DigestVerifyFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestVerifyFinal)
#define EVP_DigestVerifyInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestVerifyInit)
#define EVP_DigestVerifyUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_DigestVerifyUpdate)
#define EVP_ENCODE_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_ENCODE_CTX_free)
#define EVP_ENCODE_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_ENCODE_CTX_new)
#define EVP_EncodeBlock BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_EncodeBlock)
#define EVP_EncodeFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_EncodeFinal)
#define EVP_EncodeInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_EncodeInit)
#define EVP_EncodeUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_EncodeUpdate)
#define EVP_EncodedLength BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_EncodedLength)
#define EVP_EncryptFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_EncryptFinal)
#define EVP_EncryptFinal_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_EncryptFinal_ex)
#define EVP_EncryptInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_EncryptInit)
#define EVP_EncryptInit_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_EncryptInit_ex)
#define EVP_EncryptUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_EncryptUpdate)
#define EVP_HPKE_AEAD_aead BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_AEAD_aead)
#define EVP_HPKE_AEAD_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_AEAD_id)
#define EVP_HPKE_CTX_aead BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_aead)
#define EVP_HPKE_CTX_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_cleanup)
#define EVP_HPKE_CTX_export BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_export)
#define EVP_HPKE_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_free)
#define EVP_HPKE_CTX_kdf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_kdf)
#define EVP_HPKE_CTX_kem BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_kem)
#define EVP_HPKE_CTX_max_overhead BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_max_overhead)
#define EVP_HPKE_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_new)
#define EVP_HPKE_CTX_open BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_open)
#define EVP_HPKE_CTX_seal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_seal)
#define EVP_HPKE_CTX_setup_auth_recipient BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_setup_auth_recipient)
#define EVP_HPKE_CTX_setup_auth_sender BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_setup_auth_sender)
#define EVP_HPKE_CTX_setup_auth_sender_with_seed_for_testing BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_setup_auth_sender_with_seed_for_testing)
#define EVP_HPKE_CTX_setup_recipient BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_setup_recipient)
#define EVP_HPKE_CTX_setup_sender BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_setup_sender)
#define EVP_HPKE_CTX_setup_sender_with_seed_for_testing BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_setup_sender_with_seed_for_testing)
#define EVP_HPKE_CTX_zero BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_CTX_zero)
#define EVP_HPKE_KDF_hkdf_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KDF_hkdf_md)
#define EVP_HPKE_KDF_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KDF_id)
#define EVP_HPKE_KEM_enc_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEM_enc_len)
#define EVP_HPKE_KEM_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEM_id)
#define EVP_HPKE_KEM_private_key_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEM_private_key_len)
#define EVP_HPKE_KEM_public_key_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEM_public_key_len)
#define EVP_HPKE_KEY_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_cleanup)
#define EVP_HPKE_KEY_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_copy)
#define EVP_HPKE_KEY_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_free)
#define EVP_HPKE_KEY_generate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_generate)
#define EVP_HPKE_KEY_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_init)
#define EVP_HPKE_KEY_kem BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_kem)
#define EVP_HPKE_KEY_move BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_move)
#define EVP_HPKE_KEY_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_new)
#define EVP_HPKE_KEY_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_private_key)
#define EVP_HPKE_KEY_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_public_key)
#define EVP_HPKE_KEY_zero BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_HPKE_KEY_zero)
#define EVP_MD_CTX_block_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_block_size)
#define EVP_MD_CTX_cleanse BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_cleanse)
#define EVP_MD_CTX_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_cleanup)
#define EVP_MD_CTX_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_copy)
#define EVP_MD_CTX_copy_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_copy_ex)
#define EVP_MD_CTX_create BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_create)
#define EVP_MD_CTX_destroy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_destroy)
#define EVP_MD_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_free)
#define EVP_MD_CTX_get_pkey_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_get_pkey_ctx)
#define EVP_MD_CTX_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_init)
#define EVP_MD_CTX_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_md)
#define EVP_MD_CTX_move BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_move)
#define EVP_MD_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_new)
#define EVP_MD_CTX_pkey_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_pkey_ctx)
#define EVP_MD_CTX_reset BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_reset)
#define EVP_MD_CTX_set_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_set_flags)
#define EVP_MD_CTX_set_pkey_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_set_pkey_ctx)
#define EVP_MD_CTX_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_size)
#define EVP_MD_CTX_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_CTX_type)
#define EVP_MD_block_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_block_size)
#define EVP_MD_do_all BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_do_all)
#define EVP_MD_do_all_sorted BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_do_all_sorted)
#define EVP_MD_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_flags)
#define EVP_MD_get0_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_get0_name)
#define EVP_MD_get_pkey_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_get_pkey_type)
#define EVP_MD_meth_get_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_meth_get_flags)
#define EVP_MD_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_name)
#define EVP_MD_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_nid)
#define EVP_MD_pkey_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_pkey_type)
#define EVP_MD_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_size)
#define EVP_MD_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_type)
#define EVP_MD_unstable_sha3_enable BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_unstable_sha3_enable)
#define EVP_MD_unstable_sha3_is_enabled BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_MD_unstable_sha3_is_enabled)
#define EVP_PBE_scrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PBE_scrypt)
#define EVP_PKCS82PKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKCS82PKEY)
#define EVP_PKEY2PKCS8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY2PKCS8)
#define EVP_PKEY_CTX_add1_hkdf_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_add1_hkdf_info)
#define EVP_PKEY_CTX_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_ctrl)
#define EVP_PKEY_CTX_ctrl_str BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_ctrl_str)
#define EVP_PKEY_CTX_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_dup)
#define EVP_PKEY_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_free)
#define EVP_PKEY_CTX_get0_pkey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_get0_pkey)
#define EVP_PKEY_CTX_get0_rsa_oaep_label BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_get0_rsa_oaep_label)
#define EVP_PKEY_CTX_get0_signature_context BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_get0_signature_context)
#define EVP_PKEY_CTX_get_app_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_get_app_data)
#define EVP_PKEY_CTX_get_keygen_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_get_keygen_info)
#define EVP_PKEY_CTX_get_rsa_mgf1_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_get_rsa_mgf1_md)
#define EVP_PKEY_CTX_get_rsa_oaep_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_get_rsa_oaep_md)
#define EVP_PKEY_CTX_get_rsa_padding BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_get_rsa_padding)
#define EVP_PKEY_CTX_get_rsa_pss_saltlen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_get_rsa_pss_saltlen)
#define EVP_PKEY_CTX_get_signature_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_get_signature_md)
#define EVP_PKEY_CTX_hkdf_mode BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_hkdf_mode)
#define EVP_PKEY_CTX_kem_set_params BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_kem_set_params)
#define EVP_PKEY_CTX_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_md)
#define EVP_PKEY_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_new)
#define EVP_PKEY_CTX_new_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_new_id)
#define EVP_PKEY_CTX_pqdsa_set_params BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_pqdsa_set_params)
#define EVP_PKEY_CTX_set0_rsa_oaep_label BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set0_rsa_oaep_label)
#define EVP_PKEY_CTX_set1_hkdf_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set1_hkdf_key)
#define EVP_PKEY_CTX_set1_hkdf_salt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set1_hkdf_salt)
#define EVP_PKEY_CTX_set_app_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_app_data)
#define EVP_PKEY_CTX_set_cb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_cb)
#define EVP_PKEY_CTX_set_dh_pad BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_dh_pad)
#define EVP_PKEY_CTX_set_dh_paramgen_generator BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_dh_paramgen_generator)
#define EVP_PKEY_CTX_set_dh_paramgen_prime_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_dh_paramgen_prime_len)
#define EVP_PKEY_CTX_set_dsa_paramgen_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_dsa_paramgen_bits)
#define EVP_PKEY_CTX_set_dsa_paramgen_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_dsa_paramgen_md)
#define EVP_PKEY_CTX_set_dsa_paramgen_q_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_dsa_paramgen_q_bits)
#define EVP_PKEY_CTX_set_ec_param_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_ec_param_enc)
#define EVP_PKEY_CTX_set_ec_paramgen_curve_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_ec_paramgen_curve_nid)
#define EVP_PKEY_CTX_set_hkdf_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_hkdf_md)
#define EVP_PKEY_CTX_set_rsa_keygen_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_rsa_keygen_bits)
#define EVP_PKEY_CTX_set_rsa_keygen_pubexp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_rsa_keygen_pubexp)
#define EVP_PKEY_CTX_set_rsa_mgf1_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_rsa_mgf1_md)
#define EVP_PKEY_CTX_set_rsa_oaep_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_rsa_oaep_md)
#define EVP_PKEY_CTX_set_rsa_padding BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_rsa_padding)
#define EVP_PKEY_CTX_set_rsa_pss_keygen_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_rsa_pss_keygen_md)
#define EVP_PKEY_CTX_set_rsa_pss_keygen_mgf1_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_rsa_pss_keygen_mgf1_md)
#define EVP_PKEY_CTX_set_rsa_pss_keygen_saltlen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_rsa_pss_keygen_saltlen)
#define EVP_PKEY_CTX_set_rsa_pss_saltlen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_rsa_pss_saltlen)
#define EVP_PKEY_CTX_set_signature_context BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_signature_context)
#define EVP_PKEY_CTX_set_signature_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_CTX_set_signature_md)
#define EVP_PKEY_asn1_find BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_asn1_find)
#define EVP_PKEY_asn1_find_str BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_asn1_find_str)
#define EVP_PKEY_asn1_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_asn1_get0)
#define EVP_PKEY_asn1_get0_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_asn1_get0_info)
#define EVP_PKEY_asn1_get_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_asn1_get_count)
#define EVP_PKEY_assign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_assign)
#define EVP_PKEY_assign_DH BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_assign_DH)
#define EVP_PKEY_assign_DSA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_assign_DSA)
#define EVP_PKEY_assign_EC_KEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_assign_EC_KEY)
#define EVP_PKEY_assign_RSA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_assign_RSA)
#define EVP_PKEY_base_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_base_id)
#define EVP_PKEY_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_bits)
#define EVP_PKEY_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_cmp)
#define EVP_PKEY_cmp_parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_cmp_parameters)
#define EVP_PKEY_copy_parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_copy_parameters)
#define EVP_PKEY_decapsulate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_decapsulate)
#define EVP_PKEY_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_decrypt)
#define EVP_PKEY_decrypt_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_decrypt_init)
#define EVP_PKEY_derive BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_derive)
#define EVP_PKEY_derive_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_derive_init)
#define EVP_PKEY_derive_set_peer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_derive_set_peer)
#define EVP_PKEY_ec_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_ec_pkey_meth)
#define EVP_PKEY_ed25519_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_ed25519_pkey_meth)
#define EVP_PKEY_ed25519ph_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_ed25519ph_pkey_meth)
#define EVP_PKEY_encapsulate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_encapsulate)
#define EVP_PKEY_encapsulate_deterministic BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_encapsulate_deterministic)
#define EVP_PKEY_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_encrypt)
#define EVP_PKEY_encrypt_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_encrypt_init)
#define EVP_PKEY_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_free)
#define EVP_PKEY_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get0)
#define EVP_PKEY_get0_DH BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get0_DH)
#define EVP_PKEY_get0_DSA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get0_DSA)
#define EVP_PKEY_get0_EC_KEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get0_EC_KEY)
#define EVP_PKEY_get0_RSA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get0_RSA)
#define EVP_PKEY_get1_DH BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get1_DH)
#define EVP_PKEY_get1_DSA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get1_DSA)
#define EVP_PKEY_get1_EC_KEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get1_EC_KEY)
#define EVP_PKEY_get1_RSA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get1_RSA)
#define EVP_PKEY_get1_tls_encodedpoint BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get1_tls_encodedpoint)
#define EVP_PKEY_get_raw_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get_raw_private_key)
#define EVP_PKEY_get_raw_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_get_raw_public_key)
#define EVP_PKEY_hkdf_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_hkdf_pkey_meth)
#define EVP_PKEY_hmac_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_hmac_pkey_meth)
#define EVP_PKEY_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_id)
#define EVP_PKEY_is_opaque BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_is_opaque)
#define EVP_PKEY_kem_check_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_kem_check_key)
#define EVP_PKEY_kem_new_raw_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_kem_new_raw_key)
#define EVP_PKEY_kem_new_raw_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_kem_new_raw_public_key)
#define EVP_PKEY_kem_new_raw_secret_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_kem_new_raw_secret_key)
#define EVP_PKEY_kem_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_kem_pkey_meth)
#define EVP_PKEY_keygen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_keygen)
#define EVP_PKEY_keygen_deterministic BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_keygen_deterministic)
#define EVP_PKEY_keygen_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_keygen_init)
#define EVP_PKEY_missing_parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_missing_parameters)
#define EVP_PKEY_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_new)
#define EVP_PKEY_new_mac_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_new_mac_key)
#define EVP_PKEY_new_raw_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_new_raw_private_key)
#define EVP_PKEY_new_raw_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_new_raw_public_key)
#define EVP_PKEY_paramgen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_paramgen)
#define EVP_PKEY_paramgen_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_paramgen_init)
#define EVP_PKEY_pqdsa_new_raw_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_pqdsa_new_raw_private_key)
#define EVP_PKEY_pqdsa_new_raw_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_pqdsa_new_raw_public_key)
#define EVP_PKEY_pqdsa_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_pqdsa_pkey_meth)
#define EVP_PKEY_pqdsa_set_params BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_pqdsa_set_params)
#define EVP_PKEY_print_params BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_print_params)
#define EVP_PKEY_print_private BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_print_private)
#define EVP_PKEY_print_public BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_print_public)
#define EVP_PKEY_rsa_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_rsa_pkey_meth)
#define EVP_PKEY_rsa_pss_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_rsa_pss_pkey_meth)
#define EVP_PKEY_set1_DH BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_set1_DH)
#define EVP_PKEY_set1_DSA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_set1_DSA)
#define EVP_PKEY_set1_EC_KEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_set1_EC_KEY)
#define EVP_PKEY_set1_RSA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_set1_RSA)
#define EVP_PKEY_set1_tls_encodedpoint BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_set1_tls_encodedpoint)
#define EVP_PKEY_set_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_set_type)
#define EVP_PKEY_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_sign)
#define EVP_PKEY_sign_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_sign_init)
#define EVP_PKEY_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_size)
#define EVP_PKEY_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_type)
#define EVP_PKEY_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_up_ref)
#define EVP_PKEY_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_verify)
#define EVP_PKEY_verify_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_verify_init)
#define EVP_PKEY_verify_recover BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_verify_recover)
#define EVP_PKEY_verify_recover_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_PKEY_verify_recover_init)
#define EVP_RSA_PKEY_CTX_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_RSA_PKEY_CTX_ctrl)
#define EVP_SignFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_SignFinal)
#define EVP_SignInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_SignInit)
#define EVP_SignInit_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_SignInit_ex)
#define EVP_SignUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_SignUpdate)
#define EVP_VerifyFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_VerifyFinal)
#define EVP_VerifyInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_VerifyInit)
#define EVP_VerifyInit_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_VerifyInit_ex)
#define EVP_VerifyUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_VerifyUpdate)
#define EVP_add_cipher_alias BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_add_cipher_alias)
#define EVP_add_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_add_digest)
#define EVP_aead_aes_128_cbc_sha1_tls BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_cbc_sha1_tls)
#define EVP_aead_aes_128_cbc_sha1_tls_implicit_iv BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_cbc_sha1_tls_implicit_iv)
#define EVP_aead_aes_128_cbc_sha256_tls BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_cbc_sha256_tls)
#define EVP_aead_aes_128_cbc_sha256_tls_implicit_iv BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_cbc_sha256_tls_implicit_iv)
#define EVP_aead_aes_128_ccm_bluetooth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_ccm_bluetooth)
#define EVP_aead_aes_128_ccm_bluetooth_8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_ccm_bluetooth_8)
#define EVP_aead_aes_128_ccm_matter BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_ccm_matter)
#define EVP_aead_aes_128_ctr_hmac_sha256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_ctr_hmac_sha256)
#define EVP_aead_aes_128_gcm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_gcm)
#define EVP_aead_aes_128_gcm_randnonce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_gcm_randnonce)
#define EVP_aead_aes_128_gcm_siv BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_gcm_siv)
#define EVP_aead_aes_128_gcm_tls12 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_gcm_tls12)
#define EVP_aead_aes_128_gcm_tls13 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_128_gcm_tls13)
#define EVP_aead_aes_192_gcm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_192_gcm)
#define EVP_aead_aes_256_cbc_sha1_tls BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_256_cbc_sha1_tls)
#define EVP_aead_aes_256_cbc_sha1_tls_implicit_iv BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_256_cbc_sha1_tls_implicit_iv)
#define EVP_aead_aes_256_cbc_sha384_tls BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_256_cbc_sha384_tls)
#define EVP_aead_aes_256_ctr_hmac_sha256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_256_ctr_hmac_sha256)
#define EVP_aead_aes_256_gcm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_256_gcm)
#define EVP_aead_aes_256_gcm_randnonce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_256_gcm_randnonce)
#define EVP_aead_aes_256_gcm_siv BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_256_gcm_siv)
#define EVP_aead_aes_256_gcm_tls12 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_256_gcm_tls12)
#define EVP_aead_aes_256_gcm_tls13 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_aes_256_gcm_tls13)
#define EVP_aead_chacha20_poly1305 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_chacha20_poly1305)
#define EVP_aead_des_ede3_cbc_sha1_tls BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_des_ede3_cbc_sha1_tls)
#define EVP_aead_des_ede3_cbc_sha1_tls_implicit_iv BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_des_ede3_cbc_sha1_tls_implicit_iv)
#define EVP_aead_null_sha1_tls BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_null_sha1_tls)
#define EVP_aead_xchacha20_poly1305 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aead_xchacha20_poly1305)
#define EVP_aes_128_cbc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_cbc)
#define EVP_aes_128_cbc_hmac_sha1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_cbc_hmac_sha1)
#define EVP_aes_128_cbc_hmac_sha256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_cbc_hmac_sha256)
#define EVP_aes_128_ccm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_ccm)
#define EVP_aes_128_cfb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_cfb)
#define EVP_aes_128_cfb1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_cfb1)
#define EVP_aes_128_cfb128 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_cfb128)
#define EVP_aes_128_cfb8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_cfb8)
#define EVP_aes_128_ctr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_ctr)
#define EVP_aes_128_ecb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_ecb)
#define EVP_aes_128_gcm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_gcm)
#define EVP_aes_128_ofb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_128_ofb)
#define EVP_aes_192_cbc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_192_cbc)
#define EVP_aes_192_ccm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_192_ccm)
#define EVP_aes_192_cfb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_192_cfb)
#define EVP_aes_192_cfb1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_192_cfb1)
#define EVP_aes_192_cfb128 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_192_cfb128)
#define EVP_aes_192_cfb8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_192_cfb8)
#define EVP_aes_192_ctr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_192_ctr)
#define EVP_aes_192_ecb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_192_ecb)
#define EVP_aes_192_gcm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_192_gcm)
#define EVP_aes_192_ofb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_192_ofb)
#define EVP_aes_256_cbc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_cbc)
#define EVP_aes_256_cbc_hmac_sha1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_cbc_hmac_sha1)
#define EVP_aes_256_cbc_hmac_sha256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_cbc_hmac_sha256)
#define EVP_aes_256_ccm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_ccm)
#define EVP_aes_256_cfb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_cfb)
#define EVP_aes_256_cfb1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_cfb1)
#define EVP_aes_256_cfb128 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_cfb128)
#define EVP_aes_256_cfb8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_cfb8)
#define EVP_aes_256_ctr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_ctr)
#define EVP_aes_256_ecb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_ecb)
#define EVP_aes_256_gcm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_gcm)
#define EVP_aes_256_ofb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_ofb)
#define EVP_aes_256_wrap BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_wrap)
#define EVP_aes_256_xts BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_aes_256_xts)
#define EVP_bf_cbc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_bf_cbc)
#define EVP_bf_cfb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_bf_cfb)
#define EVP_bf_ecb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_bf_ecb)
#define EVP_blake2b256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_blake2b256)
#define EVP_cast5_cbc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_cast5_cbc)
#define EVP_cast5_ecb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_cast5_ecb)
#define EVP_chacha20_poly1305 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_chacha20_poly1305)
#define EVP_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_cleanup)
#define EVP_des_cbc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_des_cbc)
#define EVP_des_ecb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_des_ecb)
#define EVP_des_ede BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_des_ede)
#define EVP_des_ede3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_des_ede3)
#define EVP_des_ede3_cbc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_des_ede3_cbc)
#define EVP_des_ede3_ecb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_des_ede3_ecb)
#define EVP_des_ede_cbc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_des_ede_cbc)
#define EVP_enc_null BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_enc_null)
#define EVP_final_with_secret_suffix_sha1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_final_with_secret_suffix_sha1)
#define EVP_final_with_secret_suffix_sha256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_final_with_secret_suffix_sha256)
#define EVP_final_with_secret_suffix_sha384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_final_with_secret_suffix_sha384)
#define EVP_get_cipherbyname BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_get_cipherbyname)
#define EVP_get_cipherbynid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_get_cipherbynid)
#define EVP_get_digestbyname BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_get_digestbyname)
#define EVP_get_digestbynid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_get_digestbynid)
#define EVP_get_digestbyobj BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_get_digestbyobj)
#define EVP_has_aes_hardware BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_has_aes_hardware)
#define EVP_hpke_aes_128_gcm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_hpke_aes_128_gcm)
#define EVP_hpke_aes_256_gcm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_hpke_aes_256_gcm)
#define EVP_hpke_chacha20_poly1305 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_hpke_chacha20_poly1305)
#define EVP_hpke_hkdf_sha256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_hpke_hkdf_sha256)
#define EVP_hpke_x25519_hkdf_sha256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_hpke_x25519_hkdf_sha256)
#define EVP_marshal_digest_algorithm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_marshal_digest_algorithm)
#define EVP_marshal_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_marshal_private_key)
#define EVP_marshal_private_key_v2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_marshal_private_key_v2)
#define EVP_marshal_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_marshal_public_key)
#define EVP_md4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_md4)
#define EVP_md5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_md5)
#define EVP_md5_sha1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_md5_sha1)
#define EVP_md_null BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_md_null)
#define EVP_parse_digest_algorithm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_parse_digest_algorithm)
#define EVP_parse_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_parse_private_key)
#define EVP_parse_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_parse_public_key)
#define EVP_rc2_40_cbc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_rc2_40_cbc)
#define EVP_rc2_cbc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_rc2_cbc)
#define EVP_rc4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_rc4)
#define EVP_ripemd160 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_ripemd160)
#define EVP_sha1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha1)
#define EVP_sha224 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha224)
#define EVP_sha256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha256)
#define EVP_sha384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha384)
#define EVP_sha3_224 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha3_224)
#define EVP_sha3_256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha3_256)
#define EVP_sha3_384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha3_384)
#define EVP_sha3_512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha3_512)
#define EVP_sha512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha512)
#define EVP_sha512_224 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha512_224)
#define EVP_sha512_256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_sha512_256)
#define EVP_shake128 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_shake128)
#define EVP_shake256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_shake256)
#define EVP_tls_cbc_copy_mac BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_tls_cbc_copy_mac)
#define EVP_tls_cbc_digest_record BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_tls_cbc_digest_record)
#define EVP_tls_cbc_record_digest_supported BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_tls_cbc_record_digest_supported)
#define EVP_tls_cbc_remove_padding BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EVP_tls_cbc_remove_padding)
#define EXTENDED_KEY_USAGE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EXTENDED_KEY_USAGE_free)
#define EXTENDED_KEY_USAGE_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EXTENDED_KEY_USAGE_it)
#define EXTENDED_KEY_USAGE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, EXTENDED_KEY_USAGE_new)
#define FIPS_is_entropy_cpu_jitter BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, FIPS_is_entropy_cpu_jitter)
#define FIPS_mode BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, FIPS_mode)
#define FIPS_mode_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, FIPS_mode_set)
#define FIPS_read_counter BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, FIPS_read_counter)
#define FIPS_service_indicator_after_call BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, FIPS_service_indicator_after_call)
#define FIPS_service_indicator_before_call BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, FIPS_service_indicator_before_call)
#define GENERAL_NAMES_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAMES_free)
#define GENERAL_NAMES_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAMES_it)
#define GENERAL_NAMES_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAMES_new)
#define GENERAL_NAME_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAME_cmp)
#define GENERAL_NAME_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAME_dup)
#define GENERAL_NAME_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAME_free)
#define GENERAL_NAME_get0_otherName BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAME_get0_otherName)
#define GENERAL_NAME_get0_value BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAME_get0_value)
#define GENERAL_NAME_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAME_it)
#define GENERAL_NAME_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAME_new)
#define GENERAL_NAME_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAME_print)
#define GENERAL_NAME_set0_othername BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAME_set0_othername)
#define GENERAL_NAME_set0_value BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_NAME_set0_value)
#define GENERAL_SUBTREE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_SUBTREE_free)
#define GENERAL_SUBTREE_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_SUBTREE_it)
#define GENERAL_SUBTREE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, GENERAL_SUBTREE_new)
#define HKDF BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HKDF)
#define HKDF_expand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HKDF_expand)
#define HKDF_extract BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HKDF_extract)
#define HMAC BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC)
#define HMAC_CTX_cleanse BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_CTX_cleanse)
#define HMAC_CTX_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_CTX_cleanup)
#define HMAC_CTX_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_CTX_copy)
#define HMAC_CTX_copy_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_CTX_copy_ex)
#define HMAC_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_CTX_free)
#define HMAC_CTX_get_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_CTX_get_md)
#define HMAC_CTX_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_CTX_init)
#define HMAC_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_CTX_new)
#define HMAC_CTX_reset BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_CTX_reset)
#define HMAC_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_Final)
#define HMAC_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_Init)
#define HMAC_Init_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_Init_ex)
#define HMAC_Init_from_precomputed_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_Init_from_precomputed_key)
#define HMAC_KEY_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_KEY_copy)
#define HMAC_KEY_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_KEY_new)
#define HMAC_KEY_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_KEY_set)
#define HMAC_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_Update)
#define HMAC_get_precomputed_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_get_precomputed_key)
#define HMAC_set_precomputed_key_export BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_set_precomputed_key_export)
#define HMAC_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_size)
#define HMAC_with_precompute BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HMAC_with_precompute)
#define HRSS_decap BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HRSS_decap)
#define HRSS_encap BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HRSS_encap)
#define HRSS_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HRSS_generate_key)
#define HRSS_marshal_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HRSS_marshal_public_key)
#define HRSS_parse_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HRSS_parse_public_key)
#define HRSS_poly3_invert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HRSS_poly3_invert)
#define HRSS_poly3_mul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, HRSS_poly3_mul)
#define ISSUING_DIST_POINT_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ISSUING_DIST_POINT_free)
#define ISSUING_DIST_POINT_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ISSUING_DIST_POINT_it)
#define ISSUING_DIST_POINT_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ISSUING_DIST_POINT_new)
#define KBKDF_ctr_hmac BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, KBKDF_ctr_hmac)
#define KEM_KEY_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, KEM_KEY_free)
#define KEM_KEY_get0_kem BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, KEM_KEY_get0_kem)
#define KEM_KEY_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, KEM_KEY_init)
#define KEM_KEY_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, KEM_KEY_new)
#define KEM_KEY_set_raw_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, KEM_KEY_set_raw_key)
#define KEM_KEY_set_raw_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, KEM_KEY_set_raw_public_key)
#define KEM_KEY_set_raw_secret_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, KEM_KEY_set_raw_secret_key)
#define KEM_find_kem_by_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, KEM_find_kem_by_nid)
#define Keccak1600_Absorb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, Keccak1600_Absorb)
#define Keccak1600_Absorb_cext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, Keccak1600_Absorb_cext)
#define Keccak1600_Absorb_hw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, Keccak1600_Absorb_hw)
#define Keccak1600_Squeeze BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, Keccak1600_Squeeze)
#define Keccak1600_Squeeze_cext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, Keccak1600_Squeeze_cext)
#define Keccak1600_Squeeze_hw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, Keccak1600_Squeeze_hw)
#define MD4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD4)
#define MD4_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD4_Final)
#define MD4_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD4_Init)
#define MD4_Transform BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD4_Transform)
#define MD4_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD4_Update)
#define MD5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD5)
#define MD5_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD5_Final)
#define MD5_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD5_Init)
#define MD5_Init_from_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD5_Init_from_state)
#define MD5_Transform BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD5_Transform)
#define MD5_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD5_Update)
#define MD5_get_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MD5_get_state)
#define MGF1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, MGF1)
#define NAME_CONSTRAINTS_check BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NAME_CONSTRAINTS_check)
#define NAME_CONSTRAINTS_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NAME_CONSTRAINTS_free)
#define NAME_CONSTRAINTS_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NAME_CONSTRAINTS_it)
#define NAME_CONSTRAINTS_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NAME_CONSTRAINTS_new)
#define NCONF_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NCONF_free)
#define NCONF_get_section BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NCONF_get_section)
#define NCONF_get_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NCONF_get_string)
#define NCONF_load BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NCONF_load)
#define NCONF_load_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NCONF_load_bio)
#define NCONF_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NCONF_new)
#define NETSCAPE_SPKAC_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKAC_free)
#define NETSCAPE_SPKAC_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKAC_it)
#define NETSCAPE_SPKAC_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKAC_new)
#define NETSCAPE_SPKI_b64_decode BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKI_b64_decode)
#define NETSCAPE_SPKI_b64_encode BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKI_b64_encode)
#define NETSCAPE_SPKI_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKI_free)
#define NETSCAPE_SPKI_get_pubkey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKI_get_pubkey)
#define NETSCAPE_SPKI_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKI_it)
#define NETSCAPE_SPKI_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKI_new)
#define NETSCAPE_SPKI_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKI_print)
#define NETSCAPE_SPKI_set_pubkey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKI_set_pubkey)
#define NETSCAPE_SPKI_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKI_sign)
#define NETSCAPE_SPKI_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NETSCAPE_SPKI_verify)
#define NOTICEREF_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NOTICEREF_free)
#define NOTICEREF_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NOTICEREF_it)
#define NOTICEREF_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, NOTICEREF_new)
#define OBJ_NAME_do_all_sorted BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_NAME_do_all_sorted)
#define OBJ_cbs2nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_cbs2nid)
#define OBJ_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_cleanup)
#define OBJ_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_cmp)
#define OBJ_create BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_create)
#define OBJ_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_dup)
#define OBJ_find_sigid_algs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_find_sigid_algs)
#define OBJ_find_sigid_by_algs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_find_sigid_by_algs)
#define OBJ_get0_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_get0_data)
#define OBJ_get_undef BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_get_undef)
#define OBJ_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_length)
#define OBJ_ln2nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_ln2nid)
#define OBJ_nid2cbb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_nid2cbb)
#define OBJ_nid2ln BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_nid2ln)
#define OBJ_nid2obj BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_nid2obj)
#define OBJ_nid2sn BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_nid2sn)
#define OBJ_obj2nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_obj2nid)
#define OBJ_obj2txt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_obj2txt)
#define OBJ_sn2nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_sn2nid)
#define OBJ_txt2nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_txt2nid)
#define OBJ_txt2obj BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OBJ_txt2obj)
#define OCSP_BASICRESP_add_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_BASICRESP_add_ext)
#define OCSP_BASICRESP_delete_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_BASICRESP_delete_ext)
#define OCSP_BASICRESP_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_BASICRESP_free)
#define OCSP_BASICRESP_get_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_BASICRESP_get_ext)
#define OCSP_BASICRESP_get_ext_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_BASICRESP_get_ext_by_NID)
#define OCSP_BASICRESP_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_BASICRESP_it)
#define OCSP_BASICRESP_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_BASICRESP_new)
#define OCSP_CERTID_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_CERTID_dup)
#define OCSP_CERTID_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_CERTID_free)
#define OCSP_CERTID_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_CERTID_it)
#define OCSP_CERTID_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_CERTID_new)
#define OCSP_CERTSTATUS_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_CERTSTATUS_it)
#define OCSP_ONEREQ_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_ONEREQ_free)
#define OCSP_ONEREQ_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_ONEREQ_it)
#define OCSP_ONEREQ_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_ONEREQ_new)
#define OCSP_REQINFO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQINFO_free)
#define OCSP_REQINFO_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQINFO_it)
#define OCSP_REQINFO_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQINFO_new)
#define OCSP_REQUEST_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQUEST_free)
#define OCSP_REQUEST_get_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQUEST_get_ext)
#define OCSP_REQUEST_get_ext_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQUEST_get_ext_by_NID)
#define OCSP_REQUEST_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQUEST_it)
#define OCSP_REQUEST_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQUEST_new)
#define OCSP_REQUEST_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQUEST_print)
#define OCSP_REQ_CTX_add1_header BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQ_CTX_add1_header)
#define OCSP_REQ_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQ_CTX_free)
#define OCSP_REQ_CTX_get0_mem_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQ_CTX_get0_mem_bio)
#define OCSP_REQ_CTX_http BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQ_CTX_http)
#define OCSP_REQ_CTX_i2d BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQ_CTX_i2d)
#define OCSP_REQ_CTX_nbio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQ_CTX_nbio)
#define OCSP_REQ_CTX_nbio_d2i BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQ_CTX_nbio_d2i)
#define OCSP_REQ_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQ_CTX_new)
#define OCSP_REQ_CTX_set1_req BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REQ_CTX_set1_req)
#define OCSP_RESPBYTES_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPBYTES_free)
#define OCSP_RESPBYTES_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPBYTES_it)
#define OCSP_RESPBYTES_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPBYTES_new)
#define OCSP_RESPDATA_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPDATA_free)
#define OCSP_RESPDATA_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPDATA_it)
#define OCSP_RESPDATA_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPDATA_new)
#define OCSP_RESPID_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPID_it)
#define OCSP_RESPONSE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPONSE_free)
#define OCSP_RESPONSE_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPONSE_it)
#define OCSP_RESPONSE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPONSE_new)
#define OCSP_RESPONSE_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_RESPONSE_print)
#define OCSP_REVOKEDINFO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REVOKEDINFO_free)
#define OCSP_REVOKEDINFO_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REVOKEDINFO_it)
#define OCSP_REVOKEDINFO_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_REVOKEDINFO_new)
#define OCSP_SIGNATURE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_SIGNATURE_free)
#define OCSP_SIGNATURE_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_SIGNATURE_it)
#define OCSP_SIGNATURE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_SIGNATURE_new)
#define OCSP_SINGLERESP_add_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_SINGLERESP_add_ext)
#define OCSP_SINGLERESP_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_SINGLERESP_free)
#define OCSP_SINGLERESP_get0_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_SINGLERESP_get0_id)
#define OCSP_SINGLERESP_get_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_SINGLERESP_get_ext)
#define OCSP_SINGLERESP_get_ext_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_SINGLERESP_get_ext_count)
#define OCSP_SINGLERESP_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_SINGLERESP_it)
#define OCSP_SINGLERESP_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_SINGLERESP_new)
#define OCSP_basic_add1_cert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_basic_add1_cert)
#define OCSP_basic_add1_nonce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_basic_add1_nonce)
#define OCSP_basic_add1_status BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_basic_add1_status)
#define OCSP_basic_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_basic_sign)
#define OCSP_basic_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_basic_verify)
#define OCSP_cert_id_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_cert_id_new)
#define OCSP_cert_status_str BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_cert_status_str)
#define OCSP_cert_to_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_cert_to_id)
#define OCSP_check_nonce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_check_nonce)
#define OCSP_check_validity BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_check_validity)
#define OCSP_copy_nonce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_copy_nonce)
#define OCSP_crl_reason_str BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_crl_reason_str)
#define OCSP_get_default_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_get_default_digest)
#define OCSP_id_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_id_cmp)
#define OCSP_id_get0_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_id_get0_info)
#define OCSP_id_issuer_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_id_issuer_cmp)
#define OCSP_onereq_get0_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_onereq_get0_id)
#define OCSP_parse_url BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_parse_url)
#define OCSP_request_add0_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_request_add0_id)
#define OCSP_request_add1_cert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_request_add1_cert)
#define OCSP_request_add1_nonce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_request_add1_nonce)
#define OCSP_request_is_signed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_request_is_signed)
#define OCSP_request_onereq_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_request_onereq_count)
#define OCSP_request_onereq_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_request_onereq_get0)
#define OCSP_request_set1_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_request_set1_name)
#define OCSP_request_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_request_sign)
#define OCSP_request_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_request_verify)
#define OCSP_resp_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_resp_count)
#define OCSP_resp_find BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_resp_find)
#define OCSP_resp_find_status BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_resp_find_status)
#define OCSP_resp_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_resp_get0)
#define OCSP_response_create BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_response_create)
#define OCSP_response_get1_basic BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_response_get1_basic)
#define OCSP_response_status BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_response_status)
#define OCSP_response_status_str BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_response_status_str)
#define OCSP_sendreq_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_sendreq_bio)
#define OCSP_sendreq_nbio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_sendreq_nbio)
#define OCSP_sendreq_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_sendreq_new)
#define OCSP_set_max_response_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_set_max_response_length)
#define OCSP_single_get0_status BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OCSP_single_get0_status)
#define OPENSSL_add_all_algorithms_conf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_add_all_algorithms_conf)
#define OPENSSL_armcap_P BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_armcap_P)
#define OPENSSL_asprintf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_asprintf)
#define OPENSSL_calloc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_calloc)
#define OPENSSL_cleanse BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_cleanse)
#define OPENSSL_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_cleanup)
#define OPENSSL_clear_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_clear_free)
#define OPENSSL_config BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_config)
#define OPENSSL_cpucap_initialized BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_cpucap_initialized)
#define OPENSSL_cpuid_setup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_cpuid_setup)
#define OPENSSL_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_free)
#define OPENSSL_fromxdigit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_fromxdigit)
#define OPENSSL_gmtime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_gmtime)
#define OPENSSL_gmtime_adj BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_gmtime_adj)
#define OPENSSL_gmtime_diff BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_gmtime_diff)
#define OPENSSL_hash32 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_hash32)
#define OPENSSL_hexstr2buf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_hexstr2buf)
#define OPENSSL_ia32cap_P BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_ia32cap_P)
#define OPENSSL_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_init)
#define OPENSSL_init_crypto BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_init_crypto)
#define OPENSSL_isalnum BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_isalnum)
#define OPENSSL_isalpha BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_isalpha)
#define OPENSSL_isdigit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_isdigit)
#define OPENSSL_isspace BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_isspace)
#define OPENSSL_isxdigit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_isxdigit)
#define OPENSSL_lh_delete BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_lh_delete)
#define OPENSSL_lh_doall_arg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_lh_doall_arg)
#define OPENSSL_lh_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_lh_free)
#define OPENSSL_lh_insert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_lh_insert)
#define OPENSSL_lh_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_lh_new)
#define OPENSSL_lh_num_items BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_lh_num_items)
#define OPENSSL_lh_retrieve BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_lh_retrieve)
#define OPENSSL_lh_retrieve_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_lh_retrieve_key)
#define OPENSSL_load_builtin_modules BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_load_builtin_modules)
#define OPENSSL_malloc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_malloc)
#define OPENSSL_malloc_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_malloc_init)
#define OPENSSL_memdup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_memdup)
#define OPENSSL_no_config BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_no_config)
#define OPENSSL_posix_to_tm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_posix_to_tm)
#define OPENSSL_ppc64le_hwcap2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_ppc64le_hwcap2)
#define OPENSSL_realloc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_realloc)
#define OPENSSL_secure_clear_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_secure_clear_free)
#define OPENSSL_secure_malloc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_secure_malloc)
#define OPENSSL_secure_zalloc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_secure_zalloc)
#define OPENSSL_sk_deep_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_deep_copy)
#define OPENSSL_sk_delete BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_delete)
#define OPENSSL_sk_delete_if BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_delete_if)
#define OPENSSL_sk_delete_ptr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_delete_ptr)
#define OPENSSL_sk_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_dup)
#define OPENSSL_sk_find BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_find)
#define OPENSSL_sk_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_free)
#define OPENSSL_sk_insert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_insert)
#define OPENSSL_sk_is_sorted BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_is_sorted)
#define OPENSSL_sk_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_new)
#define OPENSSL_sk_new_null BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_new_null)
#define OPENSSL_sk_num BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_num)
#define OPENSSL_sk_pop BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_pop)
#define OPENSSL_sk_pop_free_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_pop_free_ex)
#define OPENSSL_sk_push BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_push)
#define OPENSSL_sk_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_set)
#define OPENSSL_sk_set_cmp_func BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_set_cmp_func)
#define OPENSSL_sk_shift BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_shift)
#define OPENSSL_sk_sort BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_sort)
#define OPENSSL_sk_unshift BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_unshift)
#define OPENSSL_sk_value BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_value)
#define OPENSSL_sk_zero BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_sk_zero)
#define OPENSSL_strcasecmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_strcasecmp)
#define OPENSSL_strdup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_strdup)
#define OPENSSL_strhash BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_strhash)
#define OPENSSL_strlcat BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_strlcat)
#define OPENSSL_strlcpy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_strlcpy)
#define OPENSSL_strncasecmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_strncasecmp)
#define OPENSSL_strndup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_strndup)
#define OPENSSL_strnlen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_strnlen)
#define OPENSSL_timegm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_timegm)
#define OPENSSL_tm_to_posix BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_tm_to_posix)
#define OPENSSL_tolower BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_tolower)
#define OPENSSL_vasprintf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_vasprintf)
#define OPENSSL_vasprintf_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_vasprintf_internal)
#define OPENSSL_zalloc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OPENSSL_zalloc)
#define OTHERNAME_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OTHERNAME_free)
#define OTHERNAME_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OTHERNAME_it)
#define OTHERNAME_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OTHERNAME_new)
#define OpenSSL_add_all_algorithms BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OpenSSL_add_all_algorithms)
#define OpenSSL_add_all_ciphers BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OpenSSL_add_all_ciphers)
#define OpenSSL_add_all_digests BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OpenSSL_add_all_digests)
#define OpenSSL_version BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OpenSSL_version)
#define OpenSSL_version_num BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, OpenSSL_version_num)
#define PEM_ASN1_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_ASN1_read)
#define PEM_ASN1_read_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_ASN1_read_bio)
#define PEM_ASN1_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_ASN1_write)
#define PEM_ASN1_write_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_ASN1_write_bio)
#define PEM_X509_INFO_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_X509_INFO_read)
#define PEM_X509_INFO_read_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_X509_INFO_read_bio)
#define PEM_X509_INFO_write_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_X509_INFO_write_bio)
#define PEM_bytes_read_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_bytes_read_bio)
#define PEM_def_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_def_callback)
#define PEM_dek_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_dek_info)
#define PEM_do_header BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_do_header)
#define PEM_get_EVP_CIPHER_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_get_EVP_CIPHER_INFO)
#define PEM_proc_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_proc_type)
#define PEM_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read)
#define PEM_read_DHparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_DHparams)
#define PEM_read_DSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_DSAPrivateKey)
#define PEM_read_DSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_DSA_PUBKEY)
#define PEM_read_DSAparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_DSAparams)
#define PEM_read_ECPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_ECPrivateKey)
#define PEM_read_EC_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_EC_PUBKEY)
#define PEM_read_PKCS7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_PKCS7)
#define PEM_read_PKCS8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_PKCS8)
#define PEM_read_PKCS8_PRIV_KEY_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_PKCS8_PRIV_KEY_INFO)
#define PEM_read_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_PUBKEY)
#define PEM_read_PrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_PrivateKey)
#define PEM_read_RSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_RSAPrivateKey)
#define PEM_read_RSAPublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_RSAPublicKey)
#define PEM_read_RSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_RSA_PUBKEY)
#define PEM_read_X509 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_X509)
#define PEM_read_X509_AUX BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_X509_AUX)
#define PEM_read_X509_CRL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_X509_CRL)
#define PEM_read_X509_REQ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_X509_REQ)
#define PEM_read_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio)
#define PEM_read_bio_DHparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_DHparams)
#define PEM_read_bio_DSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_DSAPrivateKey)
#define PEM_read_bio_DSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_DSA_PUBKEY)
#define PEM_read_bio_DSAparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_DSAparams)
#define PEM_read_bio_ECPKParameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_ECPKParameters)
#define PEM_read_bio_ECPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_ECPrivateKey)
#define PEM_read_bio_EC_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_EC_PUBKEY)
#define PEM_read_bio_PKCS7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_PKCS7)
#define PEM_read_bio_PKCS8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_PKCS8)
#define PEM_read_bio_PKCS8_PRIV_KEY_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_PKCS8_PRIV_KEY_INFO)
#define PEM_read_bio_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_PUBKEY)
#define PEM_read_bio_Parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_Parameters)
#define PEM_read_bio_PrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_PrivateKey)
#define PEM_read_bio_RSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_RSAPrivateKey)
#define PEM_read_bio_RSAPublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_RSAPublicKey)
#define PEM_read_bio_RSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_RSA_PUBKEY)
#define PEM_read_bio_X509 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_X509)
#define PEM_read_bio_X509_AUX BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_X509_AUX)
#define PEM_read_bio_X509_CRL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_X509_CRL)
#define PEM_read_bio_X509_REQ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_read_bio_X509_REQ)
#define PEM_write BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write)
#define PEM_write_DHparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_DHparams)
#define PEM_write_DSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_DSAPrivateKey)
#define PEM_write_DSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_DSA_PUBKEY)
#define PEM_write_DSAparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_DSAparams)
#define PEM_write_ECPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_ECPrivateKey)
#define PEM_write_EC_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_EC_PUBKEY)
#define PEM_write_PKCS7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_PKCS7)
#define PEM_write_PKCS8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_PKCS8)
#define PEM_write_PKCS8PrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_PKCS8PrivateKey)
#define PEM_write_PKCS8PrivateKey_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_PKCS8PrivateKey_nid)
#define PEM_write_PKCS8_PRIV_KEY_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_PKCS8_PRIV_KEY_INFO)
#define PEM_write_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_PUBKEY)
#define PEM_write_PrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_PrivateKey)
#define PEM_write_RSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_RSAPrivateKey)
#define PEM_write_RSAPublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_RSAPublicKey)
#define PEM_write_RSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_RSA_PUBKEY)
#define PEM_write_X509 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_X509)
#define PEM_write_X509_AUX BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_X509_AUX)
#define PEM_write_X509_CRL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_X509_CRL)
#define PEM_write_X509_REQ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_X509_REQ)
#define PEM_write_X509_REQ_NEW BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_X509_REQ_NEW)
#define PEM_write_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio)
#define PEM_write_bio_DHparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_DHparams)
#define PEM_write_bio_DSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_DSAPrivateKey)
#define PEM_write_bio_DSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_DSA_PUBKEY)
#define PEM_write_bio_DSAparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_DSAparams)
#define PEM_write_bio_ECPKParameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_ECPKParameters)
#define PEM_write_bio_ECPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_ECPrivateKey)
#define PEM_write_bio_EC_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_EC_PUBKEY)
#define PEM_write_bio_PKCS7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_PKCS7)
#define PEM_write_bio_PKCS8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_PKCS8)
#define PEM_write_bio_PKCS8PrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_PKCS8PrivateKey)
#define PEM_write_bio_PKCS8PrivateKey_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_PKCS8PrivateKey_nid)
#define PEM_write_bio_PKCS8_PRIV_KEY_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_PKCS8_PRIV_KEY_INFO)
#define PEM_write_bio_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_PUBKEY)
#define PEM_write_bio_Parameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_Parameters)
#define PEM_write_bio_PrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_PrivateKey)
#define PEM_write_bio_PrivateKey_traditional BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_PrivateKey_traditional)
#define PEM_write_bio_RSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_RSAPrivateKey)
#define PEM_write_bio_RSAPublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_RSAPublicKey)
#define PEM_write_bio_RSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_RSA_PUBKEY)
#define PEM_write_bio_X509 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_X509)
#define PEM_write_bio_X509_AUX BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_X509_AUX)
#define PEM_write_bio_X509_CRL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_X509_CRL)
#define PEM_write_bio_X509_REQ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_X509_REQ)
#define PEM_write_bio_X509_REQ_NEW BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PEM_write_bio_X509_REQ_NEW)
#define PKCS12_PBE_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS12_PBE_add)
#define PKCS12_create BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS12_create)
#define PKCS12_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS12_free)
#define PKCS12_get_key_and_certs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS12_get_key_and_certs)
#define PKCS12_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS12_new)
#define PKCS12_parse BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS12_parse)
#define PKCS12_set_mac BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS12_set_mac)
#define PKCS12_verify_mac BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS12_verify_mac)
#define PKCS1_MGF1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS1_MGF1)
#define PKCS5_PBKDF2_HMAC BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS5_PBKDF2_HMAC)
#define PKCS5_PBKDF2_HMAC_SHA1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS5_PBKDF2_HMAC_SHA1)
#define PKCS5_pbe2_decrypt_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS5_pbe2_decrypt_init)
#define PKCS5_pbe2_encrypt_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS5_pbe2_encrypt_init)
#define PKCS7_ATTR_VERIFY_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ATTR_VERIFY_it)
#define PKCS7_DIGEST_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_DIGEST_free)
#define PKCS7_DIGEST_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_DIGEST_it)
#define PKCS7_DIGEST_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_DIGEST_new)
#define PKCS7_ENCRYPT_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ENCRYPT_free)
#define PKCS7_ENCRYPT_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ENCRYPT_it)
#define PKCS7_ENCRYPT_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ENCRYPT_new)
#define PKCS7_ENC_CONTENT_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ENC_CONTENT_free)
#define PKCS7_ENC_CONTENT_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ENC_CONTENT_it)
#define PKCS7_ENC_CONTENT_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ENC_CONTENT_new)
#define PKCS7_ENVELOPE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ENVELOPE_free)
#define PKCS7_ENVELOPE_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ENVELOPE_it)
#define PKCS7_ENVELOPE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ENVELOPE_new)
#define PKCS7_ISSUER_AND_SERIAL_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ISSUER_AND_SERIAL_free)
#define PKCS7_ISSUER_AND_SERIAL_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ISSUER_AND_SERIAL_it)
#define PKCS7_ISSUER_AND_SERIAL_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_ISSUER_AND_SERIAL_new)
#define PKCS7_RECIP_INFO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_RECIP_INFO_free)
#define PKCS7_RECIP_INFO_get0_alg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_RECIP_INFO_get0_alg)
#define PKCS7_RECIP_INFO_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_RECIP_INFO_it)
#define PKCS7_RECIP_INFO_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_RECIP_INFO_new)
#define PKCS7_RECIP_INFO_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_RECIP_INFO_set)
#define PKCS7_SIGNED_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGNED_free)
#define PKCS7_SIGNED_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGNED_it)
#define PKCS7_SIGNED_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGNED_new)
#define PKCS7_SIGNER_INFO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGNER_INFO_free)
#define PKCS7_SIGNER_INFO_get0_algs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGNER_INFO_get0_algs)
#define PKCS7_SIGNER_INFO_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGNER_INFO_it)
#define PKCS7_SIGNER_INFO_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGNER_INFO_new)
#define PKCS7_SIGNER_INFO_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGNER_INFO_set)
#define PKCS7_SIGN_ENVELOPE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGN_ENVELOPE_free)
#define PKCS7_SIGN_ENVELOPE_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGN_ENVELOPE_it)
#define PKCS7_SIGN_ENVELOPE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_SIGN_ENVELOPE_new)
#define PKCS7_add_certificate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_add_certificate)
#define PKCS7_add_crl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_add_crl)
#define PKCS7_add_recipient BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_add_recipient)
#define PKCS7_add_recipient_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_add_recipient_info)
#define PKCS7_add_signer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_add_signer)
#define PKCS7_bundle_CRLs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_bundle_CRLs)
#define PKCS7_bundle_certificates BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_bundle_certificates)
#define PKCS7_bundle_raw_certificates BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_bundle_raw_certificates)
#define PKCS7_content_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_content_new)
#define PKCS7_dataFinal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_dataFinal)
#define PKCS7_dataInit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_dataInit)
#define PKCS7_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_decrypt)
#define PKCS7_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_dup)
#define PKCS7_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_encrypt)
#define PKCS7_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_free)
#define PKCS7_get_CRLs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_get_CRLs)
#define PKCS7_get_PEM_CRLs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_get_PEM_CRLs)
#define PKCS7_get_PEM_certificates BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_get_PEM_certificates)
#define PKCS7_get_certificates BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_get_certificates)
#define PKCS7_get_detached BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_get_detached)
#define PKCS7_get_raw_certificates BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_get_raw_certificates)
#define PKCS7_get_recipient_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_get_recipient_info)
#define PKCS7_get_signed_attribute BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_get_signed_attribute)
#define PKCS7_get_signer_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_get_signer_info)
#define PKCS7_is_detached BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_is_detached)
#define PKCS7_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_it)
#define PKCS7_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_new)
#define PKCS7_print_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_print_ctx)
#define PKCS7_set_cipher BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_set_cipher)
#define PKCS7_set_content BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_set_content)
#define PKCS7_set_detached BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_set_detached)
#define PKCS7_set_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_set_digest)
#define PKCS7_set_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_set_type)
#define PKCS7_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_sign)
#define PKCS7_type_is_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_type_is_data)
#define PKCS7_type_is_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_type_is_digest)
#define PKCS7_type_is_encrypted BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_type_is_encrypted)
#define PKCS7_type_is_enveloped BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_type_is_enveloped)
#define PKCS7_type_is_signed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_type_is_signed)
#define PKCS7_type_is_signedAndEnveloped BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_type_is_signedAndEnveloped)
#define PKCS7_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS7_verify)
#define PKCS8_PRIV_KEY_INFO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS8_PRIV_KEY_INFO_free)
#define PKCS8_PRIV_KEY_INFO_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS8_PRIV_KEY_INFO_it)
#define PKCS8_PRIV_KEY_INFO_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS8_PRIV_KEY_INFO_new)
#define PKCS8_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS8_decrypt)
#define PKCS8_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS8_encrypt)
#define PKCS8_marshal_encrypted_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS8_marshal_encrypted_private_key)
#define PKCS8_parse_encrypted_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PKCS8_parse_encrypted_private_key)
#define POLICYINFO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICYINFO_free)
#define POLICYINFO_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICYINFO_it)
#define POLICYINFO_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICYINFO_new)
#define POLICYQUALINFO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICYQUALINFO_free)
#define POLICYQUALINFO_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICYQUALINFO_it)
#define POLICYQUALINFO_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICYQUALINFO_new)
#define POLICY_CONSTRAINTS_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICY_CONSTRAINTS_free)
#define POLICY_CONSTRAINTS_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICY_CONSTRAINTS_it)
#define POLICY_CONSTRAINTS_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICY_CONSTRAINTS_new)
#define POLICY_MAPPINGS_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICY_MAPPINGS_it)
#define POLICY_MAPPING_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICY_MAPPING_free)
#define POLICY_MAPPING_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICY_MAPPING_it)
#define POLICY_MAPPING_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, POLICY_MAPPING_new)
#define PQDSA_KEY_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PQDSA_KEY_free)
#define PQDSA_KEY_get0_dsa BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PQDSA_KEY_get0_dsa)
#define PQDSA_KEY_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PQDSA_KEY_init)
#define PQDSA_KEY_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PQDSA_KEY_new)
#define PQDSA_KEY_set_raw_keypair_from_seed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PQDSA_KEY_set_raw_keypair_from_seed)
#define PQDSA_KEY_set_raw_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PQDSA_KEY_set_raw_private_key)
#define PQDSA_KEY_set_raw_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PQDSA_KEY_set_raw_public_key)
#define PQDSA_find_asn1_by_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PQDSA_find_asn1_by_nid)
#define PQDSA_find_dsa_by_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, PQDSA_find_dsa_by_nid)
#define RAND_OpenSSL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_OpenSSL)
#define RAND_SSLeay BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_SSLeay)
#define RAND_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_add)
#define RAND_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_bytes)
#define RAND_bytes_with_additional_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_bytes_with_additional_data)
#define RAND_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_cleanup)
#define RAND_egd BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_egd)
#define RAND_egd_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_egd_bytes)
#define RAND_enable_fork_unsafe_buffering BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_enable_fork_unsafe_buffering)
#define RAND_file_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_file_name)
#define RAND_get_rand_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_get_rand_method)
#define RAND_get_system_entropy_for_custom_prng BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_get_system_entropy_for_custom_prng)
#define RAND_keep_random_devices_open BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_keep_random_devices_open)
#define RAND_load_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_load_file)
#define RAND_poll BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_poll)
#define RAND_priv_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_priv_bytes)
#define RAND_pseudo_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_pseudo_bytes)
#define RAND_seed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_seed)
#define RAND_set_rand_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_set_rand_method)
#define RAND_status BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_status)
#define RAND_write_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RAND_write_file)
#define RC4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RC4)
#define RC4_set_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RC4_set_key)
#define RFC8032_DOM2_PREFIX BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RFC8032_DOM2_PREFIX)
#define RIPEMD160 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RIPEMD160)
#define RIPEMD160_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RIPEMD160_Final)
#define RIPEMD160_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RIPEMD160_Init)
#define RIPEMD160_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RIPEMD160_Update)
#define RSAPrivateKey_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSAPrivateKey_dup)
#define RSAPublicKey_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSAPublicKey_dup)
#define RSASSA_PSS_PARAMS_create BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSASSA_PSS_PARAMS_create)
#define RSASSA_PSS_PARAMS_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSASSA_PSS_PARAMS_free)
#define RSASSA_PSS_PARAMS_get BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSASSA_PSS_PARAMS_get)
#define RSASSA_PSS_PARAMS_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSASSA_PSS_PARAMS_new)
#define RSASSA_PSS_parse_params BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSASSA_PSS_parse_params)
#define RSAZ_1024_mod_exp_avx2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSAZ_1024_mod_exp_avx2)
#define RSAZ_mod_exp_avx512_x2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSAZ_mod_exp_avx512_x2)
#define RSA_ALGOR_IDENTIFIER_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_ALGOR_IDENTIFIER_free)
#define RSA_ALGOR_IDENTIFIER_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_ALGOR_IDENTIFIER_new)
#define RSA_INTEGER_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_INTEGER_free)
#define RSA_INTEGER_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_INTEGER_new)
#define RSA_MGA_IDENTIFIER_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_MGA_IDENTIFIER_free)
#define RSA_MGA_IDENTIFIER_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_MGA_IDENTIFIER_new)
#define RSA_PSS_PARAMS_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_PSS_PARAMS_free)
#define RSA_PSS_PARAMS_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_PSS_PARAMS_it)
#define RSA_PSS_PARAMS_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_PSS_PARAMS_new)
#define RSA_add_pkcs1_prefix BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_add_pkcs1_prefix)
#define RSA_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_bits)
#define RSA_blinding_off_temp_for_accp_compatibility BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_blinding_off_temp_for_accp_compatibility)
#define RSA_blinding_on BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_blinding_on)
#define RSA_check_fips BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_check_fips)
#define RSA_check_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_check_key)
#define RSA_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_decrypt)
#define RSA_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_encrypt)
#define RSA_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_flags)
#define RSA_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_free)
#define RSA_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_generate_key)
#define RSA_generate_key_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_generate_key_ex)
#define RSA_generate_key_fips BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_generate_key_fips)
#define RSA_get0_crt_params BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_crt_params)
#define RSA_get0_d BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_d)
#define RSA_get0_dmp1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_dmp1)
#define RSA_get0_dmq1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_dmq1)
#define RSA_get0_e BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_e)
#define RSA_get0_factors BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_factors)
#define RSA_get0_iqmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_iqmp)
#define RSA_get0_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_key)
#define RSA_get0_n BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_n)
#define RSA_get0_p BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_p)
#define RSA_get0_pss_params BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_pss_params)
#define RSA_get0_q BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get0_q)
#define RSA_get_default_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get_default_method)
#define RSA_get_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get_ex_data)
#define RSA_get_ex_new_index BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get_ex_new_index)
#define RSA_get_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_get_method)
#define RSA_is_opaque BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_is_opaque)
#define RSA_marshal_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_marshal_private_key)
#define RSA_marshal_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_marshal_public_key)
#define RSA_meth_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_meth_free)
#define RSA_meth_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_meth_new)
#define RSA_meth_set0_app_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_meth_set0_app_data)
#define RSA_meth_set_finish BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_meth_set_finish)
#define RSA_meth_set_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_meth_set_init)
#define RSA_meth_set_priv_dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_meth_set_priv_dec)
#define RSA_meth_set_priv_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_meth_set_priv_enc)
#define RSA_meth_set_pub_dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_meth_set_pub_dec)
#define RSA_meth_set_pub_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_meth_set_pub_enc)
#define RSA_meth_set_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_meth_set_sign)
#define RSA_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_new)
#define RSA_new_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_new_method)
#define RSA_new_method_no_e BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_new_method_no_e)
#define RSA_new_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_new_private_key)
#define RSA_new_private_key_large_e BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_new_private_key_large_e)
#define RSA_new_private_key_no_crt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_new_private_key_no_crt)
#define RSA_new_private_key_no_e BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_new_private_key_no_e)
#define RSA_new_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_new_public_key)
#define RSA_new_public_key_large_e BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_new_public_key_large_e)
#define RSA_padding_add_PKCS1_OAEP BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_padding_add_PKCS1_OAEP)
#define RSA_padding_add_PKCS1_OAEP_mgf1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_padding_add_PKCS1_OAEP_mgf1)
#define RSA_padding_add_PKCS1_PSS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_padding_add_PKCS1_PSS)
#define RSA_padding_add_PKCS1_PSS_mgf1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_padding_add_PKCS1_PSS_mgf1)
#define RSA_padding_add_PKCS1_type_1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_padding_add_PKCS1_type_1)
#define RSA_padding_add_none BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_padding_add_none)
#define RSA_padding_check_PKCS1_OAEP_mgf1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_padding_check_PKCS1_OAEP_mgf1)
#define RSA_padding_check_PKCS1_type_1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_padding_check_PKCS1_type_1)
#define RSA_parse_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_parse_private_key)
#define RSA_parse_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_parse_public_key)
#define RSA_pkey_ctx_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_pkey_ctx_ctrl)
#define RSA_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_print)
#define RSA_print_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_print_fp)
#define RSA_private_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_private_decrypt)
#define RSA_private_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_private_encrypt)
#define RSA_private_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_private_key_from_bytes)
#define RSA_private_key_to_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_private_key_to_bytes)
#define RSA_public_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_public_decrypt)
#define RSA_public_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_public_encrypt)
#define RSA_public_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_public_key_from_bytes)
#define RSA_public_key_to_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_public_key_to_bytes)
#define RSA_set0_crt_params BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_set0_crt_params)
#define RSA_set0_factors BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_set0_factors)
#define RSA_set0_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_set0_key)
#define RSA_set_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_set_ex_data)
#define RSA_set_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_set_flags)
#define RSA_set_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_set_method)
#define RSA_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_sign)
#define RSA_sign_pss_mgf1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_sign_pss_mgf1)
#define RSA_sign_raw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_sign_raw)
#define RSA_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_size)
#define RSA_test_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_test_flags)
#define RSA_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_up_ref)
#define RSA_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_verify)
#define RSA_verify_PKCS1_PSS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_verify_PKCS1_PSS)
#define RSA_verify_PKCS1_PSS_mgf1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_verify_PKCS1_PSS_mgf1)
#define RSA_verify_pss_mgf1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_verify_pss_mgf1)
#define RSA_verify_raw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, RSA_verify_raw)
#define SHA1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA1)
#define SHA1_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA1_Final)
#define SHA1_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA1_Init)
#define SHA1_Init_from_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA1_Init_from_state)
#define SHA1_Transform BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA1_Transform)
#define SHA1_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA1_Update)
#define SHA1_get_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA1_get_state)
#define SHA224 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA224)
#define SHA224_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA224_Final)
#define SHA224_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA224_Init)
#define SHA224_Init_from_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA224_Init_from_state)
#define SHA224_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA224_Update)
#define SHA224_get_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA224_get_state)
#define SHA256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA256)
#define SHA256_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA256_Final)
#define SHA256_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA256_Init)
#define SHA256_Init_from_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA256_Init_from_state)
#define SHA256_Transform BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA256_Transform)
#define SHA256_TransformBlocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA256_TransformBlocks)
#define SHA256_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA256_Update)
#define SHA256_get_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA256_get_state)
#define SHA384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA384)
#define SHA384_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA384_Final)
#define SHA384_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA384_Init)
#define SHA384_Init_from_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA384_Init_from_state)
#define SHA384_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA384_Update)
#define SHA384_get_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA384_get_state)
#define SHA3_224 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA3_224)
#define SHA3_256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA3_256)
#define SHA3_384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA3_384)
#define SHA3_512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA3_512)
#define SHA3_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA3_Final)
#define SHA3_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA3_Init)
#define SHA3_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA3_Update)
#define SHA512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512)
#define SHA512_224 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_224)
#define SHA512_224_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_224_Final)
#define SHA512_224_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_224_Init)
#define SHA512_224_Init_from_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_224_Init_from_state)
#define SHA512_224_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_224_Update)
#define SHA512_224_get_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_224_get_state)
#define SHA512_256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_256)
#define SHA512_256_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_256_Final)
#define SHA512_256_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_256_Init)
#define SHA512_256_Init_from_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_256_Init_from_state)
#define SHA512_256_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_256_Update)
#define SHA512_256_get_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_256_get_state)
#define SHA512_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_Final)
#define SHA512_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_Init)
#define SHA512_Init_from_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_Init_from_state)
#define SHA512_Transform BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_Transform)
#define SHA512_Update BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_Update)
#define SHA512_get_state BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHA512_get_state)
#define SHAKE128 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHAKE128)
#define SHAKE128_Absorb_once_x4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHAKE128_Absorb_once_x4)
#define SHAKE128_Init_x4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHAKE128_Init_x4)
#define SHAKE128_Squeezeblocks_x4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHAKE128_Squeezeblocks_x4)
#define SHAKE256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHAKE256)
#define SHAKE256_x4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHAKE256_x4)
#define SHAKE_Absorb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHAKE_Absorb)
#define SHAKE_Final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHAKE_Final)
#define SHAKE_Init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHAKE_Init)
#define SHAKE_Squeeze BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SHAKE_Squeeze)
#define SIPHASH_24 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SIPHASH_24)
#define SMIME_read_PKCS7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SMIME_read_PKCS7)
#define SMIME_write_PKCS7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SMIME_write_PKCS7)
#define SPAKE2_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SPAKE2_CTX_free)
#define SPAKE2_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SPAKE2_CTX_new)
#define SPAKE2_generate_msg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SPAKE2_generate_msg)
#define SPAKE2_process_msg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SPAKE2_process_msg)
#define SSHKDF BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SSHKDF)
#define SSKDF_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SSKDF_digest)
#define SSKDF_hmac BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SSKDF_hmac)
#define SSLeay BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SSLeay)
#define SSLeay_version BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, SSLeay_version)
#define TRUST_TOKEN_CLIENT_add_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_CLIENT_add_key)
#define TRUST_TOKEN_CLIENT_begin_issuance BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_CLIENT_begin_issuance)
#define TRUST_TOKEN_CLIENT_begin_issuance_over_message BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_CLIENT_begin_issuance_over_message)
#define TRUST_TOKEN_CLIENT_begin_redemption BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_CLIENT_begin_redemption)
#define TRUST_TOKEN_CLIENT_finish_issuance BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_CLIENT_finish_issuance)
#define TRUST_TOKEN_CLIENT_finish_redemption BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_CLIENT_finish_redemption)
#define TRUST_TOKEN_CLIENT_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_CLIENT_free)
#define TRUST_TOKEN_CLIENT_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_CLIENT_new)
#define TRUST_TOKEN_CLIENT_set_srr_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_CLIENT_set_srr_key)
#define TRUST_TOKEN_ISSUER_add_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_ISSUER_add_key)
#define TRUST_TOKEN_ISSUER_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_ISSUER_free)
#define TRUST_TOKEN_ISSUER_issue BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_ISSUER_issue)
#define TRUST_TOKEN_ISSUER_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_ISSUER_new)
#define TRUST_TOKEN_ISSUER_redeem BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_ISSUER_redeem)
#define TRUST_TOKEN_ISSUER_redeem_over_message BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_ISSUER_redeem_over_message)
#define TRUST_TOKEN_ISSUER_set_metadata_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_ISSUER_set_metadata_key)
#define TRUST_TOKEN_ISSUER_set_srr_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_ISSUER_set_srr_key)
#define TRUST_TOKEN_PRETOKEN_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_PRETOKEN_free)
#define TRUST_TOKEN_decode_private_metadata BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_decode_private_metadata)
#define TRUST_TOKEN_derive_key_from_secret BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_derive_key_from_secret)
#define TRUST_TOKEN_experiment_v1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_experiment_v1)
#define TRUST_TOKEN_experiment_v2_pmb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_experiment_v2_pmb)
#define TRUST_TOKEN_experiment_v2_voprf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_experiment_v2_voprf)
#define TRUST_TOKEN_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_free)
#define TRUST_TOKEN_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_generate_key)
#define TRUST_TOKEN_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_new)
#define TRUST_TOKEN_pst_v1_pmb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_pst_v1_pmb)
#define TRUST_TOKEN_pst_v1_voprf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, TRUST_TOKEN_pst_v1_voprf)
#define USERNOTICE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, USERNOTICE_free)
#define USERNOTICE_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, USERNOTICE_it)
#define USERNOTICE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, USERNOTICE_new)
#define UTF8_getc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, UTF8_getc)
#define UTF8_putc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, UTF8_putc)
#define X25519 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X25519)
#define X25519_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X25519_keypair)
#define X25519_public_from_private BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X25519_public_from_private)
#define X509V3_EXT_CRL_add_nconf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_CRL_add_nconf)
#define X509V3_EXT_REQ_add_nconf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_REQ_add_nconf)
#define X509V3_EXT_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_add)
#define X509V3_EXT_add_alias BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_add_alias)
#define X509V3_EXT_add_nconf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_add_nconf)
#define X509V3_EXT_add_nconf_sk BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_add_nconf_sk)
#define X509V3_EXT_conf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_conf)
#define X509V3_EXT_conf_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_conf_nid)
#define X509V3_EXT_d2i BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_d2i)
#define X509V3_EXT_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_free)
#define X509V3_EXT_get BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_get)
#define X509V3_EXT_get_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_get_nid)
#define X509V3_EXT_i2d BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_i2d)
#define X509V3_EXT_nconf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_nconf)
#define X509V3_EXT_nconf_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_nconf_nid)
#define X509V3_EXT_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_print)
#define X509V3_EXT_print_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_EXT_print_fp)
#define X509V3_NAME_from_section BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_NAME_from_section)
#define X509V3_add1_i2d BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_add1_i2d)
#define X509V3_add_standard_extensions BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_add_standard_extensions)
#define X509V3_add_value BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_add_value)
#define X509V3_add_value_bool BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_add_value_bool)
#define X509V3_add_value_int BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_add_value_int)
#define X509V3_bool_from_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_bool_from_string)
#define X509V3_conf_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_conf_free)
#define X509V3_extensions_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_extensions_print)
#define X509V3_get_d2i BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_get_d2i)
#define X509V3_get_section BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_get_section)
#define X509V3_get_value_bool BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_get_value_bool)
#define X509V3_get_value_int BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_get_value_int)
#define X509V3_parse_list BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_parse_list)
#define X509V3_set_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_set_ctx)
#define X509V3_set_nconf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509V3_set_nconf)
#define X509_ALGOR_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ALGOR_cmp)
#define X509_ALGOR_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ALGOR_dup)
#define X509_ALGOR_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ALGOR_free)
#define X509_ALGOR_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ALGOR_get0)
#define X509_ALGOR_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ALGOR_it)
#define X509_ALGOR_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ALGOR_new)
#define X509_ALGOR_set0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ALGOR_set0)
#define X509_ALGOR_set_md BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ALGOR_set_md)
#define X509_ATTRIBUTE_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_count)
#define X509_ATTRIBUTE_create BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_create)
#define X509_ATTRIBUTE_create_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_create_by_NID)
#define X509_ATTRIBUTE_create_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_create_by_OBJ)
#define X509_ATTRIBUTE_create_by_txt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_create_by_txt)
#define X509_ATTRIBUTE_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_dup)
#define X509_ATTRIBUTE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_free)
#define X509_ATTRIBUTE_get0_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_get0_data)
#define X509_ATTRIBUTE_get0_object BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_get0_object)
#define X509_ATTRIBUTE_get0_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_get0_type)
#define X509_ATTRIBUTE_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_it)
#define X509_ATTRIBUTE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_new)
#define X509_ATTRIBUTE_set1_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_set1_data)
#define X509_ATTRIBUTE_set1_object BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_ATTRIBUTE_set1_object)
#define X509_CERT_AUX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CERT_AUX_free)
#define X509_CERT_AUX_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CERT_AUX_it)
#define X509_CERT_AUX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CERT_AUX_new)
#define X509_CERT_AUX_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CERT_AUX_print)
#define X509_CINF_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CINF_free)
#define X509_CINF_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CINF_it)
#define X509_CINF_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CINF_new)
#define X509_CRL_INFO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_INFO_free)
#define X509_CRL_INFO_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_INFO_it)
#define X509_CRL_INFO_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_INFO_new)
#define X509_CRL_add0_revoked BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_add0_revoked)
#define X509_CRL_add1_ext_i2d BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_add1_ext_i2d)
#define X509_CRL_add_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_add_ext)
#define X509_CRL_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_cmp)
#define X509_CRL_delete_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_delete_ext)
#define X509_CRL_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_digest)
#define X509_CRL_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_dup)
#define X509_CRL_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_free)
#define X509_CRL_get0_by_cert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get0_by_cert)
#define X509_CRL_get0_by_serial BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get0_by_serial)
#define X509_CRL_get0_extensions BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get0_extensions)
#define X509_CRL_get0_lastUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get0_lastUpdate)
#define X509_CRL_get0_nextUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get0_nextUpdate)
#define X509_CRL_get0_signature BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get0_signature)
#define X509_CRL_get_REVOKED BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_REVOKED)
#define X509_CRL_get_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_ext)
#define X509_CRL_get_ext_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_ext_by_NID)
#define X509_CRL_get_ext_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_ext_by_OBJ)
#define X509_CRL_get_ext_by_critical BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_ext_by_critical)
#define X509_CRL_get_ext_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_ext_count)
#define X509_CRL_get_ext_d2i BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_ext_d2i)
#define X509_CRL_get_issuer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_issuer)
#define X509_CRL_get_lastUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_lastUpdate)
#define X509_CRL_get_nextUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_nextUpdate)
#define X509_CRL_get_signature_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_signature_nid)
#define X509_CRL_get_version BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_get_version)
#define X509_CRL_http_nbio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_http_nbio)
#define X509_CRL_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_it)
#define X509_CRL_match BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_match)
#define X509_CRL_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_new)
#define X509_CRL_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_print)
#define X509_CRL_print_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_print_fp)
#define X509_CRL_set1_lastUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_set1_lastUpdate)
#define X509_CRL_set1_nextUpdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_set1_nextUpdate)
#define X509_CRL_set1_signature_algo BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_set1_signature_algo)
#define X509_CRL_set1_signature_value BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_set1_signature_value)
#define X509_CRL_set_issuer_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_set_issuer_name)
#define X509_CRL_set_version BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_set_version)
#define X509_CRL_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_sign)
#define X509_CRL_sign_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_sign_ctx)
#define X509_CRL_sort BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_sort)
#define X509_CRL_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_up_ref)
#define X509_CRL_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_CRL_verify)
#define X509_EXTENSIONS_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSIONS_it)
#define X509_EXTENSION_create_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_create_by_NID)
#define X509_EXTENSION_create_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_create_by_OBJ)
#define X509_EXTENSION_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_dup)
#define X509_EXTENSION_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_free)
#define X509_EXTENSION_get_critical BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_get_critical)
#define X509_EXTENSION_get_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_get_data)
#define X509_EXTENSION_get_object BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_get_object)
#define X509_EXTENSION_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_it)
#define X509_EXTENSION_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_new)
#define X509_EXTENSION_set_critical BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_set_critical)
#define X509_EXTENSION_set_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_set_data)
#define X509_EXTENSION_set_object BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_EXTENSION_set_object)
#define X509_INFO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_INFO_free)
#define X509_LOOKUP_add_dir BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_LOOKUP_add_dir)
#define X509_LOOKUP_ctrl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_LOOKUP_ctrl)
#define X509_LOOKUP_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_LOOKUP_file)
#define X509_LOOKUP_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_LOOKUP_free)
#define X509_LOOKUP_hash_dir BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_LOOKUP_hash_dir)
#define X509_LOOKUP_load_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_LOOKUP_load_file)
#define X509_NAME_ENTRIES_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRIES_it)
#define X509_NAME_ENTRY_create_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_create_by_NID)
#define X509_NAME_ENTRY_create_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_create_by_OBJ)
#define X509_NAME_ENTRY_create_by_txt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_create_by_txt)
#define X509_NAME_ENTRY_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_dup)
#define X509_NAME_ENTRY_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_free)
#define X509_NAME_ENTRY_get_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_get_data)
#define X509_NAME_ENTRY_get_object BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_get_object)
#define X509_NAME_ENTRY_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_it)
#define X509_NAME_ENTRY_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_new)
#define X509_NAME_ENTRY_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_set)
#define X509_NAME_ENTRY_set_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_set_data)
#define X509_NAME_ENTRY_set_object BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_ENTRY_set_object)
#define X509_NAME_INTERNAL_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_INTERNAL_it)
#define X509_NAME_add_entry BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_add_entry)
#define X509_NAME_add_entry_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_add_entry_by_NID)
#define X509_NAME_add_entry_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_add_entry_by_OBJ)
#define X509_NAME_add_entry_by_txt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_add_entry_by_txt)
#define X509_NAME_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_cmp)
#define X509_NAME_delete_entry BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_delete_entry)
#define X509_NAME_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_digest)
#define X509_NAME_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_dup)
#define X509_NAME_entry_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_entry_count)
#define X509_NAME_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_free)
#define X509_NAME_get0_der BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_get0_der)
#define X509_NAME_get_entry BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_get_entry)
#define X509_NAME_get_index_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_get_index_by_NID)
#define X509_NAME_get_index_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_get_index_by_OBJ)
#define X509_NAME_get_text_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_get_text_by_NID)
#define X509_NAME_get_text_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_get_text_by_OBJ)
#define X509_NAME_hash BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_hash)
#define X509_NAME_hash_old BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_hash_old)
#define X509_NAME_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_it)
#define X509_NAME_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_new)
#define X509_NAME_oneline BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_oneline)
#define X509_NAME_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_print)
#define X509_NAME_print_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_print_ex)
#define X509_NAME_print_ex_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_print_ex_fp)
#define X509_NAME_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_NAME_set)
#define X509_OBJECT_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_OBJECT_free)
#define X509_OBJECT_free_contents BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_OBJECT_free_contents)
#define X509_OBJECT_get0_X509 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_OBJECT_get0_X509)
#define X509_OBJECT_get0_X509_CRL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_OBJECT_get0_X509_CRL)
#define X509_OBJECT_get_type BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_OBJECT_get_type)
#define X509_OBJECT_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_OBJECT_new)
#define X509_OBJECT_set1_X509 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_OBJECT_set1_X509)
#define X509_OBJECT_set1_X509_CRL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_OBJECT_set1_X509_CRL)
#define X509_PUBKEY_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PUBKEY_free)
#define X509_PUBKEY_get BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PUBKEY_get)
#define X509_PUBKEY_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PUBKEY_get0)
#define X509_PUBKEY_get0_param BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PUBKEY_get0_param)
#define X509_PUBKEY_get0_public_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PUBKEY_get0_public_key)
#define X509_PUBKEY_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PUBKEY_it)
#define X509_PUBKEY_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PUBKEY_new)
#define X509_PUBKEY_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PUBKEY_set)
#define X509_PUBKEY_set0_param BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PUBKEY_set0_param)
#define X509_PURPOSE_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PURPOSE_get0)
#define X509_PURPOSE_get0_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PURPOSE_get0_name)
#define X509_PURPOSE_get0_sname BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PURPOSE_get0_sname)
#define X509_PURPOSE_get_by_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PURPOSE_get_by_id)
#define X509_PURPOSE_get_by_sname BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PURPOSE_get_by_sname)
#define X509_PURPOSE_get_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PURPOSE_get_count)
#define X509_PURPOSE_get_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PURPOSE_get_id)
#define X509_PURPOSE_get_trust BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PURPOSE_get_trust)
#define X509_PURPOSE_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_PURPOSE_set)
#define X509_REQ_INFO_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_INFO_free)
#define X509_REQ_INFO_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_INFO_it)
#define X509_REQ_INFO_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_INFO_new)
#define X509_REQ_add1_attr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_add1_attr)
#define X509_REQ_add1_attr_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_add1_attr_by_NID)
#define X509_REQ_add1_attr_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_add1_attr_by_OBJ)
#define X509_REQ_add1_attr_by_txt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_add1_attr_by_txt)
#define X509_REQ_add_extensions BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_add_extensions)
#define X509_REQ_add_extensions_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_add_extensions_nid)
#define X509_REQ_check_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_check_private_key)
#define X509_REQ_delete_attr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_delete_attr)
#define X509_REQ_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_digest)
#define X509_REQ_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_dup)
#define X509_REQ_extension_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_extension_nid)
#define X509_REQ_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_free)
#define X509_REQ_get0_pubkey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get0_pubkey)
#define X509_REQ_get0_signature BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get0_signature)
#define X509_REQ_get1_email BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get1_email)
#define X509_REQ_get_attr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get_attr)
#define X509_REQ_get_attr_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get_attr_by_NID)
#define X509_REQ_get_attr_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get_attr_by_OBJ)
#define X509_REQ_get_attr_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get_attr_count)
#define X509_REQ_get_extensions BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get_extensions)
#define X509_REQ_get_pubkey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get_pubkey)
#define X509_REQ_get_signature_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get_signature_nid)
#define X509_REQ_get_subject_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get_subject_name)
#define X509_REQ_get_version BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_get_version)
#define X509_REQ_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_it)
#define X509_REQ_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_new)
#define X509_REQ_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_print)
#define X509_REQ_print_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_print_ex)
#define X509_REQ_print_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_print_fp)
#define X509_REQ_set1_signature_algo BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_set1_signature_algo)
#define X509_REQ_set1_signature_value BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_set1_signature_value)
#define X509_REQ_set_pubkey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_set_pubkey)
#define X509_REQ_set_subject_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_set_subject_name)
#define X509_REQ_set_version BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_set_version)
#define X509_REQ_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_sign)
#define X509_REQ_sign_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_sign_ctx)
#define X509_REQ_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REQ_verify)
#define X509_REVOKED_add1_ext_i2d BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_add1_ext_i2d)
#define X509_REVOKED_add_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_add_ext)
#define X509_REVOKED_delete_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_delete_ext)
#define X509_REVOKED_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_dup)
#define X509_REVOKED_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_free)
#define X509_REVOKED_get0_extensions BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_get0_extensions)
#define X509_REVOKED_get0_revocationDate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_get0_revocationDate)
#define X509_REVOKED_get0_serialNumber BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_get0_serialNumber)
#define X509_REVOKED_get_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_get_ext)
#define X509_REVOKED_get_ext_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_get_ext_by_NID)
#define X509_REVOKED_get_ext_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_get_ext_by_OBJ)
#define X509_REVOKED_get_ext_by_critical BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_get_ext_by_critical)
#define X509_REVOKED_get_ext_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_get_ext_count)
#define X509_REVOKED_get_ext_d2i BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_get_ext_d2i)
#define X509_REVOKED_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_it)
#define X509_REVOKED_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_new)
#define X509_REVOKED_set_revocationDate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_set_revocationDate)
#define X509_REVOKED_set_serialNumber BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_REVOKED_set_serialNumber)
#define X509_SIG_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_SIG_free)
#define X509_SIG_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_SIG_get0)
#define X509_SIG_getm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_SIG_getm)
#define X509_SIG_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_SIG_it)
#define X509_SIG_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_SIG_new)
#define X509_STORE_CTX_add_custom_crit_oid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_add_custom_crit_oid)
#define X509_STORE_CTX_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_cleanup)
#define X509_STORE_CTX_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_free)
#define X509_STORE_CTX_get0_cert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get0_cert)
#define X509_STORE_CTX_get0_chain BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get0_chain)
#define X509_STORE_CTX_get0_current_crl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get0_current_crl)
#define X509_STORE_CTX_get0_current_issuer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get0_current_issuer)
#define X509_STORE_CTX_get0_param BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get0_param)
#define X509_STORE_CTX_get0_parent_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get0_parent_ctx)
#define X509_STORE_CTX_get0_store BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get0_store)
#define X509_STORE_CTX_get0_untrusted BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get0_untrusted)
#define X509_STORE_CTX_get1_certs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get1_certs)
#define X509_STORE_CTX_get1_chain BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get1_chain)
#define X509_STORE_CTX_get1_crls BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get1_crls)
#define X509_STORE_CTX_get1_issuer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get1_issuer)
#define X509_STORE_CTX_get_by_subject BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get_by_subject)
#define X509_STORE_CTX_get_chain BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get_chain)
#define X509_STORE_CTX_get_current_cert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get_current_cert)
#define X509_STORE_CTX_get_error BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get_error)
#define X509_STORE_CTX_get_error_depth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get_error_depth)
#define X509_STORE_CTX_get_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get_ex_data)
#define X509_STORE_CTX_get_ex_new_index BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_get_ex_new_index)
#define X509_STORE_CTX_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_init)
#define X509_STORE_CTX_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_new)
#define X509_STORE_CTX_set0_crls BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set0_crls)
#define X509_STORE_CTX_set0_param BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set0_param)
#define X509_STORE_CTX_set0_trusted_stack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set0_trusted_stack)
#define X509_STORE_CTX_set0_untrusted BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set0_untrusted)
#define X509_STORE_CTX_set_cert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_cert)
#define X509_STORE_CTX_set_chain BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_chain)
#define X509_STORE_CTX_set_default BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_default)
#define X509_STORE_CTX_set_depth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_depth)
#define X509_STORE_CTX_set_error BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_error)
#define X509_STORE_CTX_set_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_ex_data)
#define X509_STORE_CTX_set_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_flags)
#define X509_STORE_CTX_set_purpose BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_purpose)
#define X509_STORE_CTX_set_time BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_time)
#define X509_STORE_CTX_set_time_posix BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_time_posix)
#define X509_STORE_CTX_set_trust BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_trust)
#define X509_STORE_CTX_set_verify_cb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_verify_cb)
#define X509_STORE_CTX_set_verify_crit_oids BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_set_verify_crit_oids)
#define X509_STORE_CTX_trusted_stack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_CTX_trusted_stack)
#define X509_STORE_add_cert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_add_cert)
#define X509_STORE_add_crl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_add_crl)
#define X509_STORE_add_lookup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_add_lookup)
#define X509_STORE_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_free)
#define X509_STORE_get0_objects BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_get0_objects)
#define X509_STORE_get0_param BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_get0_param)
#define X509_STORE_get_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_get_ex_data)
#define X509_STORE_get_ex_new_index BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_get_ex_new_index)
#define X509_STORE_load_locations BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_load_locations)
#define X509_STORE_lock BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_lock)
#define X509_STORE_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_new)
#define X509_STORE_set1_param BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_set1_param)
#define X509_STORE_set_check_crl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_set_check_crl)
#define X509_STORE_set_default_paths BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_set_default_paths)
#define X509_STORE_set_depth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_set_depth)
#define X509_STORE_set_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_set_ex_data)
#define X509_STORE_set_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_set_flags)
#define X509_STORE_set_get_crl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_set_get_crl)
#define X509_STORE_set_purpose BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_set_purpose)
#define X509_STORE_set_trust BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_set_trust)
#define X509_STORE_set_verify_cb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_set_verify_cb)
#define X509_STORE_unlock BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_unlock)
#define X509_STORE_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_STORE_up_ref)
#define X509_TRUST_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_TRUST_cleanup)
#define X509_TRUST_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_TRUST_get0)
#define X509_TRUST_get0_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_TRUST_get0_name)
#define X509_TRUST_get_by_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_TRUST_get_by_id)
#define X509_TRUST_get_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_TRUST_get_count)
#define X509_TRUST_get_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_TRUST_get_flags)
#define X509_TRUST_get_trust BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_TRUST_get_trust)
#define X509_TRUST_set BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_TRUST_set)
#define X509_VAL_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VAL_free)
#define X509_VAL_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VAL_it)
#define X509_VAL_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VAL_new)
#define X509_VERIFY_PARAM_add0_policy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_add0_policy)
#define X509_VERIFY_PARAM_add1_host BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_add1_host)
#define X509_VERIFY_PARAM_clear_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_clear_flags)
#define X509_VERIFY_PARAM_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_free)
#define X509_VERIFY_PARAM_get_depth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_get_depth)
#define X509_VERIFY_PARAM_get_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_get_flags)
#define X509_VERIFY_PARAM_get_hostflags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_get_hostflags)
#define X509_VERIFY_PARAM_inherit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_inherit)
#define X509_VERIFY_PARAM_lookup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_lookup)
#define X509_VERIFY_PARAM_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_new)
#define X509_VERIFY_PARAM_set1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set1)
#define X509_VERIFY_PARAM_set1_email BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set1_email)
#define X509_VERIFY_PARAM_set1_host BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set1_host)
#define X509_VERIFY_PARAM_set1_ip BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set1_ip)
#define X509_VERIFY_PARAM_set1_ip_asc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set1_ip_asc)
#define X509_VERIFY_PARAM_set1_policies BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set1_policies)
#define X509_VERIFY_PARAM_set_depth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set_depth)
#define X509_VERIFY_PARAM_set_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set_flags)
#define X509_VERIFY_PARAM_set_hostflags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set_hostflags)
#define X509_VERIFY_PARAM_set_purpose BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set_purpose)
#define X509_VERIFY_PARAM_set_time BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set_time)
#define X509_VERIFY_PARAM_set_time_posix BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set_time_posix)
#define X509_VERIFY_PARAM_set_trust BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_VERIFY_PARAM_set_trust)
#define X509_add1_ext_i2d BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_add1_ext_i2d)
#define X509_add1_reject_object BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_add1_reject_object)
#define X509_add1_trust_object BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_add1_trust_object)
#define X509_add_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_add_ext)
#define X509_alias_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_alias_get0)
#define X509_alias_set1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_alias_set1)
#define X509_chain_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_chain_up_ref)
#define X509_check_akid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_check_akid)
#define X509_check_ca BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_check_ca)
#define X509_check_email BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_check_email)
#define X509_check_host BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_check_host)
#define X509_check_ip BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_check_ip)
#define X509_check_ip_asc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_check_ip_asc)
#define X509_check_issued BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_check_issued)
#define X509_check_private_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_check_private_key)
#define X509_check_purpose BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_check_purpose)
#define X509_check_trust BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_check_trust)
#define X509_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_cmp)
#define X509_cmp_current_time BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_cmp_current_time)
#define X509_cmp_time BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_cmp_time)
#define X509_cmp_time_posix BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_cmp_time_posix)
#define X509_delete_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_delete_ext)
#define X509_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_digest)
#define X509_dup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_dup)
#define X509_email_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_email_free)
#define X509_find_by_issuer_and_serial BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_find_by_issuer_and_serial)
#define X509_find_by_subject BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_find_by_subject)
#define X509_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_free)
#define X509_get0_authority_issuer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_authority_issuer)
#define X509_get0_authority_key_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_authority_key_id)
#define X509_get0_authority_serial BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_authority_serial)
#define X509_get0_extensions BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_extensions)
#define X509_get0_notAfter BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_notAfter)
#define X509_get0_notBefore BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_notBefore)
#define X509_get0_pubkey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_pubkey)
#define X509_get0_pubkey_bitstr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_pubkey_bitstr)
#define X509_get0_serialNumber BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_serialNumber)
#define X509_get0_signature BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_signature)
#define X509_get0_subject_key_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_subject_key_id)
#define X509_get0_tbs_sigalg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_tbs_sigalg)
#define X509_get0_uids BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get0_uids)
#define X509_get1_email BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get1_email)
#define X509_get1_ocsp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get1_ocsp)
#define X509_get_X509_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_X509_PUBKEY)
#define X509_get_default_cert_area BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_default_cert_area)
#define X509_get_default_cert_dir BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_default_cert_dir)
#define X509_get_default_cert_dir_env BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_default_cert_dir_env)
#define X509_get_default_cert_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_default_cert_file)
#define X509_get_default_cert_file_env BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_default_cert_file_env)
#define X509_get_default_private_dir BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_default_private_dir)
#define X509_get_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_ex_data)
#define X509_get_ex_new_index BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_ex_new_index)
#define X509_get_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_ext)
#define X509_get_ext_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_ext_by_NID)
#define X509_get_ext_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_ext_by_OBJ)
#define X509_get_ext_by_critical BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_ext_by_critical)
#define X509_get_ext_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_ext_count)
#define X509_get_ext_d2i BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_ext_d2i)
#define X509_get_extended_key_usage BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_extended_key_usage)
#define X509_get_extension_flags BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_extension_flags)
#define X509_get_issuer_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_issuer_name)
#define X509_get_key_usage BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_key_usage)
#define X509_get_notAfter BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_notAfter)
#define X509_get_notBefore BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_notBefore)
#define X509_get_pathlen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_pathlen)
#define X509_get_pubkey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_pubkey)
#define X509_get_serialNumber BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_serialNumber)
#define X509_get_signature_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_signature_info)
#define X509_get_signature_nid BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_signature_nid)
#define X509_get_subject_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_subject_name)
#define X509_get_version BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_get_version)
#define X509_getm_notAfter BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_getm_notAfter)
#define X509_getm_notBefore BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_getm_notBefore)
#define X509_gmtime_adj BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_gmtime_adj)
#define X509_issuer_name_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_issuer_name_cmp)
#define X509_issuer_name_hash BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_issuer_name_hash)
#define X509_issuer_name_hash_old BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_issuer_name_hash_old)
#define X509_it BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_it)
#define X509_keyid_get0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_keyid_get0)
#define X509_keyid_set1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_keyid_set1)
#define X509_load_cert_crl_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_load_cert_crl_file)
#define X509_load_cert_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_load_cert_file)
#define X509_load_crl_file BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_load_crl_file)
#define X509_new BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_new)
#define X509_parse_from_buffer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_parse_from_buffer)
#define X509_policy_check BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_policy_check)
#define X509_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_print)
#define X509_print_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_print_ex)
#define X509_print_ex_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_print_ex_fp)
#define X509_print_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_print_fp)
#define X509_pubkey_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_pubkey_digest)
#define X509_reject_clear BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_reject_clear)
#define X509_set1_notAfter BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set1_notAfter)
#define X509_set1_notBefore BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set1_notBefore)
#define X509_set1_signature_algo BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set1_signature_algo)
#define X509_set1_signature_value BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set1_signature_value)
#define X509_set_ex_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set_ex_data)
#define X509_set_issuer_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set_issuer_name)
#define X509_set_notAfter BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set_notAfter)
#define X509_set_notBefore BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set_notBefore)
#define X509_set_pubkey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set_pubkey)
#define X509_set_serialNumber BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set_serialNumber)
#define X509_set_subject_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set_subject_name)
#define X509_set_version BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_set_version)
#define X509_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_sign)
#define X509_sign_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_sign_ctx)
#define X509_signature_dump BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_signature_dump)
#define X509_signature_print BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_signature_print)
#define X509_subject_name_cmp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_subject_name_cmp)
#define X509_subject_name_hash BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_subject_name_hash)
#define X509_subject_name_hash_old BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_subject_name_hash_old)
#define X509_supported_extension BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_supported_extension)
#define X509_time_adj BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_time_adj)
#define X509_time_adj_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_time_adj_ex)
#define X509_trust_clear BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_trust_clear)
#define X509_up_ref BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_up_ref)
#define X509_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_verify)
#define X509_verify_cert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_verify_cert)
#define X509_verify_cert_error_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509_verify_cert_error_string)
#define X509v3_add_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509v3_add_ext)
#define X509v3_delete_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509v3_delete_ext)
#define X509v3_get_ext BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509v3_get_ext)
#define X509v3_get_ext_by_NID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509v3_get_ext_by_NID)
#define X509v3_get_ext_by_OBJ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509v3_get_ext_by_OBJ)
#define X509v3_get_ext_by_critical BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509v3_get_ext_by_critical)
#define X509v3_get_ext_count BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, X509v3_get_ext_count)
#define __local_stdio_printf_options BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, __local_stdio_printf_options)
#define __local_stdio_scanf_options BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, __local_stdio_scanf_options)
#define a2i_IPADDRESS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, a2i_IPADDRESS)
#define a2i_IPADDRESS_NC BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, a2i_IPADDRESS_NC)
#define abi_test_bad_unwind_epilog BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_bad_unwind_epilog)
#define abi_test_bad_unwind_temporary BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_bad_unwind_temporary)
#define abi_test_bad_unwind_wrong_register BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_bad_unwind_wrong_register)
#define abi_test_clobber_cr0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_cr0)
#define abi_test_clobber_cr1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_cr1)
#define abi_test_clobber_cr2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_cr2)
#define abi_test_clobber_cr3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_cr3)
#define abi_test_clobber_cr4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_cr4)
#define abi_test_clobber_cr5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_cr5)
#define abi_test_clobber_cr6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_cr6)
#define abi_test_clobber_cr7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_cr7)
#define abi_test_clobber_ctr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_ctr)
#define abi_test_clobber_d0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d0)
#define abi_test_clobber_d1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d1)
#define abi_test_clobber_d10 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d10)
#define abi_test_clobber_d11 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d11)
#define abi_test_clobber_d12 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d12)
#define abi_test_clobber_d13 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d13)
#define abi_test_clobber_d14 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d14)
#define abi_test_clobber_d15 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d15)
#define abi_test_clobber_d16 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d16)
#define abi_test_clobber_d17 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d17)
#define abi_test_clobber_d18 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d18)
#define abi_test_clobber_d19 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d19)
#define abi_test_clobber_d2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d2)
#define abi_test_clobber_d20 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d20)
#define abi_test_clobber_d21 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d21)
#define abi_test_clobber_d22 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d22)
#define abi_test_clobber_d23 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d23)
#define abi_test_clobber_d24 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d24)
#define abi_test_clobber_d25 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d25)
#define abi_test_clobber_d26 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d26)
#define abi_test_clobber_d27 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d27)
#define abi_test_clobber_d28 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d28)
#define abi_test_clobber_d29 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d29)
#define abi_test_clobber_d3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d3)
#define abi_test_clobber_d30 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d30)
#define abi_test_clobber_d31 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d31)
#define abi_test_clobber_d4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d4)
#define abi_test_clobber_d5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d5)
#define abi_test_clobber_d6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d6)
#define abi_test_clobber_d7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d7)
#define abi_test_clobber_d8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d8)
#define abi_test_clobber_d9 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_d9)
#define abi_test_clobber_eax BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_eax)
#define abi_test_clobber_ebp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_ebp)
#define abi_test_clobber_ebx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_ebx)
#define abi_test_clobber_ecx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_ecx)
#define abi_test_clobber_edi BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_edi)
#define abi_test_clobber_edx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_edx)
#define abi_test_clobber_esi BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_esi)
#define abi_test_clobber_f0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f0)
#define abi_test_clobber_f1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f1)
#define abi_test_clobber_f10 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f10)
#define abi_test_clobber_f11 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f11)
#define abi_test_clobber_f12 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f12)
#define abi_test_clobber_f13 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f13)
#define abi_test_clobber_f14 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f14)
#define abi_test_clobber_f15 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f15)
#define abi_test_clobber_f16 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f16)
#define abi_test_clobber_f17 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f17)
#define abi_test_clobber_f18 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f18)
#define abi_test_clobber_f19 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f19)
#define abi_test_clobber_f2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f2)
#define abi_test_clobber_f20 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f20)
#define abi_test_clobber_f21 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f21)
#define abi_test_clobber_f22 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f22)
#define abi_test_clobber_f23 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f23)
#define abi_test_clobber_f24 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f24)
#define abi_test_clobber_f25 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f25)
#define abi_test_clobber_f26 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f26)
#define abi_test_clobber_f27 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f27)
#define abi_test_clobber_f28 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f28)
#define abi_test_clobber_f29 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f29)
#define abi_test_clobber_f3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f3)
#define abi_test_clobber_f30 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f30)
#define abi_test_clobber_f31 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f31)
#define abi_test_clobber_f4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f4)
#define abi_test_clobber_f5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f5)
#define abi_test_clobber_f6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f6)
#define abi_test_clobber_f7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f7)
#define abi_test_clobber_f8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f8)
#define abi_test_clobber_f9 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_f9)
#define abi_test_clobber_lr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_lr)
#define abi_test_clobber_r0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r0)
#define abi_test_clobber_r1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r1)
#define abi_test_clobber_r10 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r10)
#define abi_test_clobber_r11 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r11)
#define abi_test_clobber_r12 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r12)
#define abi_test_clobber_r13 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r13)
#define abi_test_clobber_r14 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r14)
#define abi_test_clobber_r15 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r15)
#define abi_test_clobber_r16 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r16)
#define abi_test_clobber_r17 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r17)
#define abi_test_clobber_r18 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r18)
#define abi_test_clobber_r19 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r19)
#define abi_test_clobber_r2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r2)
#define abi_test_clobber_r20 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r20)
#define abi_test_clobber_r21 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r21)
#define abi_test_clobber_r22 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r22)
#define abi_test_clobber_r23 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r23)
#define abi_test_clobber_r24 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r24)
#define abi_test_clobber_r25 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r25)
#define abi_test_clobber_r26 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r26)
#define abi_test_clobber_r27 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r27)
#define abi_test_clobber_r28 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r28)
#define abi_test_clobber_r29 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r29)
#define abi_test_clobber_r3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r3)
#define abi_test_clobber_r30 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r30)
#define abi_test_clobber_r31 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r31)
#define abi_test_clobber_r4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r4)
#define abi_test_clobber_r5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r5)
#define abi_test_clobber_r6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r6)
#define abi_test_clobber_r7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r7)
#define abi_test_clobber_r8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r8)
#define abi_test_clobber_r9 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_r9)
#define abi_test_clobber_rax BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_rax)
#define abi_test_clobber_rbp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_rbp)
#define abi_test_clobber_rbx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_rbx)
#define abi_test_clobber_rcx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_rcx)
#define abi_test_clobber_rdi BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_rdi)
#define abi_test_clobber_rdx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_rdx)
#define abi_test_clobber_rsi BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_rsi)
#define abi_test_clobber_v0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v0)
#define abi_test_clobber_v1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v1)
#define abi_test_clobber_v10 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v10)
#define abi_test_clobber_v10_upper BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v10_upper)
#define abi_test_clobber_v11 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v11)
#define abi_test_clobber_v11_upper BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v11_upper)
#define abi_test_clobber_v12 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v12)
#define abi_test_clobber_v12_upper BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v12_upper)
#define abi_test_clobber_v13 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v13)
#define abi_test_clobber_v13_upper BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v13_upper)
#define abi_test_clobber_v14 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v14)
#define abi_test_clobber_v14_upper BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v14_upper)
#define abi_test_clobber_v15 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v15)
#define abi_test_clobber_v15_upper BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v15_upper)
#define abi_test_clobber_v16 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v16)
#define abi_test_clobber_v17 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v17)
#define abi_test_clobber_v18 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v18)
#define abi_test_clobber_v19 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v19)
#define abi_test_clobber_v2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v2)
#define abi_test_clobber_v20 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v20)
#define abi_test_clobber_v21 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v21)
#define abi_test_clobber_v22 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v22)
#define abi_test_clobber_v23 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v23)
#define abi_test_clobber_v24 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v24)
#define abi_test_clobber_v25 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v25)
#define abi_test_clobber_v26 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v26)
#define abi_test_clobber_v27 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v27)
#define abi_test_clobber_v28 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v28)
#define abi_test_clobber_v29 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v29)
#define abi_test_clobber_v3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v3)
#define abi_test_clobber_v30 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v30)
#define abi_test_clobber_v31 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v31)
#define abi_test_clobber_v4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v4)
#define abi_test_clobber_v5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v5)
#define abi_test_clobber_v6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v6)
#define abi_test_clobber_v7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v7)
#define abi_test_clobber_v8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v8)
#define abi_test_clobber_v8_upper BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v8_upper)
#define abi_test_clobber_v9 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v9)
#define abi_test_clobber_v9_upper BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_v9_upper)
#define abi_test_clobber_x0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x0)
#define abi_test_clobber_x1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x1)
#define abi_test_clobber_x10 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x10)
#define abi_test_clobber_x11 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x11)
#define abi_test_clobber_x12 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x12)
#define abi_test_clobber_x13 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x13)
#define abi_test_clobber_x14 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x14)
#define abi_test_clobber_x15 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x15)
#define abi_test_clobber_x16 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x16)
#define abi_test_clobber_x17 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x17)
#define abi_test_clobber_x19 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x19)
#define abi_test_clobber_x2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x2)
#define abi_test_clobber_x20 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x20)
#define abi_test_clobber_x21 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x21)
#define abi_test_clobber_x22 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x22)
#define abi_test_clobber_x23 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x23)
#define abi_test_clobber_x24 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x24)
#define abi_test_clobber_x25 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x25)
#define abi_test_clobber_x26 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x26)
#define abi_test_clobber_x27 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x27)
#define abi_test_clobber_x28 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x28)
#define abi_test_clobber_x29 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x29)
#define abi_test_clobber_x3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x3)
#define abi_test_clobber_x4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x4)
#define abi_test_clobber_x5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x5)
#define abi_test_clobber_x6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x6)
#define abi_test_clobber_x7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x7)
#define abi_test_clobber_x8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x8)
#define abi_test_clobber_x9 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_x9)
#define abi_test_clobber_xmm0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm0)
#define abi_test_clobber_xmm1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm1)
#define abi_test_clobber_xmm10 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm10)
#define abi_test_clobber_xmm11 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm11)
#define abi_test_clobber_xmm12 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm12)
#define abi_test_clobber_xmm13 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm13)
#define abi_test_clobber_xmm14 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm14)
#define abi_test_clobber_xmm15 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm15)
#define abi_test_clobber_xmm2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm2)
#define abi_test_clobber_xmm3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm3)
#define abi_test_clobber_xmm4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm4)
#define abi_test_clobber_xmm5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm5)
#define abi_test_clobber_xmm6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm6)
#define abi_test_clobber_xmm7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm7)
#define abi_test_clobber_xmm8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm8)
#define abi_test_clobber_xmm9 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_clobber_xmm9)
#define abi_test_get_and_clear_direction_flag BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_get_and_clear_direction_flag)
#define abi_test_set_direction_flag BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_set_direction_flag)
#define abi_test_trampoline BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_trampoline)
#define abi_test_unwind_return BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_unwind_return)
#define abi_test_unwind_start BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_unwind_start)
#define abi_test_unwind_stop BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, abi_test_unwind_stop)
#define aes128gcmsiv_aes_ks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes128gcmsiv_aes_ks)
#define aes128gcmsiv_aes_ks_enc_x1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes128gcmsiv_aes_ks_enc_x1)
#define aes128gcmsiv_dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes128gcmsiv_dec)
#define aes128gcmsiv_ecb_enc_block BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes128gcmsiv_ecb_enc_block)
#define aes128gcmsiv_enc_msg_x4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes128gcmsiv_enc_msg_x4)
#define aes128gcmsiv_enc_msg_x8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes128gcmsiv_enc_msg_x8)
#define aes128gcmsiv_kdf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes128gcmsiv_kdf)
#define aes256gcmsiv_aes_ks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes256gcmsiv_aes_ks)
#define aes256gcmsiv_aes_ks_enc_x1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes256gcmsiv_aes_ks_enc_x1)
#define aes256gcmsiv_dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes256gcmsiv_dec)
#define aes256gcmsiv_ecb_enc_block BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes256gcmsiv_ecb_enc_block)
#define aes256gcmsiv_enc_msg_x4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes256gcmsiv_enc_msg_x4)
#define aes256gcmsiv_enc_msg_x8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes256gcmsiv_enc_msg_x8)
#define aes256gcmsiv_kdf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes256gcmsiv_kdf)
#define aes_ctr_set_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_ctr_set_key)
#define aes_gcm_dec_kernel BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_gcm_dec_kernel)
#define aes_gcm_decrypt_avx512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_gcm_decrypt_avx512)
#define aes_gcm_enc_kernel BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_gcm_enc_kernel)
#define aes_gcm_encrypt_avx512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_gcm_encrypt_avx512)
#define aes_hw_cbc_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_cbc_encrypt)
#define aes_hw_ccm64_decrypt_blocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_ccm64_decrypt_blocks)
#define aes_hw_ccm64_encrypt_blocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_ccm64_encrypt_blocks)
#define aes_hw_ctr32_encrypt_blocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_ctr32_encrypt_blocks)
#define aes_hw_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_decrypt)
#define aes_hw_ecb_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_ecb_encrypt)
#define aes_hw_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_encrypt)
#define aes_hw_set_decrypt_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_set_decrypt_key)
#define aes_hw_set_encrypt_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_set_encrypt_key)
#define aes_hw_xts_cipher BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_xts_cipher)
#define aes_hw_xts_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_xts_decrypt)
#define aes_hw_xts_decrypt_avx512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_xts_decrypt_avx512)
#define aes_hw_xts_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_xts_encrypt)
#define aes_hw_xts_encrypt_avx512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_hw_xts_encrypt_avx512)
#define aes_nohw_cbc_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_nohw_cbc_encrypt)
#define aes_nohw_ctr32_encrypt_blocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_nohw_ctr32_encrypt_blocks)
#define aes_nohw_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_nohw_decrypt)
#define aes_nohw_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_nohw_encrypt)
#define aes_nohw_set_decrypt_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_nohw_set_decrypt_key)
#define aes_nohw_set_encrypt_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aes_nohw_set_encrypt_key)
#define aesgcmsiv_htable6_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesgcmsiv_htable6_init)
#define aesgcmsiv_htable_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesgcmsiv_htable_init)
#define aesgcmsiv_htable_polyval BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesgcmsiv_htable_polyval)
#define aesgcmsiv_polyval_horner BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesgcmsiv_polyval_horner)
#define aesni_cbc_sha1_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesni_cbc_sha1_enc)
#define aesni_cbc_sha256_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesni_cbc_sha256_enc)
#define aesni_gcm_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesni_gcm_decrypt)
#define aesni_gcm_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesni_gcm_encrypt)
#define aesv8_gcm_8x_dec_128 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesv8_gcm_8x_dec_128)
#define aesv8_gcm_8x_dec_192 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesv8_gcm_8x_dec_192)
#define aesv8_gcm_8x_dec_256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesv8_gcm_8x_dec_256)
#define aesv8_gcm_8x_enc_128 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesv8_gcm_8x_enc_128)
#define aesv8_gcm_8x_enc_192 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesv8_gcm_8x_enc_192)
#define aesv8_gcm_8x_enc_256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, aesv8_gcm_8x_enc_256)
#define armv8_disable_dit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, armv8_disable_dit)
#define armv8_enable_dit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, armv8_enable_dit)
#define armv8_get_dit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, armv8_get_dit)
#define armv8_restore_dit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, armv8_restore_dit)
#define armv8_set_dit BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, armv8_set_dit)
#define asn1_bit_string_length BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_bit_string_length)
#define asn1_do_adb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_do_adb)
#define asn1_enc_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_enc_free)
#define asn1_enc_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_enc_init)
#define asn1_enc_restore BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_enc_restore)
#define asn1_enc_save BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_enc_save)
#define asn1_encoding_clear BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_encoding_clear)
#define asn1_evp_pkey_methods BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_evp_pkey_methods)
#define asn1_evp_pkey_methods_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_evp_pkey_methods_size)
#define asn1_generalizedtime_to_tm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_generalizedtime_to_tm)
#define asn1_get_choice_selector BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_get_choice_selector)
#define asn1_get_field_ptr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_get_field_ptr)
#define asn1_get_object_maybe_indefinite BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_get_object_maybe_indefinite)
#define asn1_get_string_table_for_testing BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_get_string_table_for_testing)
#define asn1_is_printable BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_is_printable)
#define asn1_item_combine_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_item_combine_free)
#define asn1_refcount_dec_and_test_zero BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_refcount_dec_and_test_zero)
#define asn1_refcount_set_one BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_refcount_set_one)
#define asn1_set_choice_selector BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_set_choice_selector)
#define asn1_type_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_type_cleanup)
#define asn1_type_set0_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_type_set0_string)
#define asn1_type_value_as_pointer BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_type_value_as_pointer)
#define asn1_utctime_to_tm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, asn1_utctime_to_tm)
#define awslc_api_version_num BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, awslc_api_version_num)
#define awslc_version_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, awslc_version_string)
#define beeu_mod_inverse_vartime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, beeu_mod_inverse_vartime)
#define bignum_add_p384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_add_p384)
#define bignum_add_p521 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_add_p521)
#define bignum_copy_row_from_table BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_copy_row_from_table)
#define bignum_copy_row_from_table_16 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_copy_row_from_table_16)
#define bignum_copy_row_from_table_32 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_copy_row_from_table_32)
#define bignum_copy_row_from_table_8n BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_copy_row_from_table_8n)
#define bignum_deamont_p384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_deamont_p384)
#define bignum_deamont_p384_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_deamont_p384_alt)
#define bignum_emontredc_8n BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_emontredc_8n)
#define bignum_fromlebytes_6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_fromlebytes_6)
#define bignum_fromlebytes_p521 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_fromlebytes_p521)
#define bignum_ge BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_ge)
#define bignum_inv_p521 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_inv_p521)
#define bignum_kmul_16_32 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_kmul_16_32)
#define bignum_kmul_32_64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_kmul_32_64)
#define bignum_ksqr_16_32 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_ksqr_16_32)
#define bignum_ksqr_32_64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_ksqr_32_64)
#define bignum_littleendian_6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_littleendian_6)
#define bignum_madd_n25519 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_madd_n25519)
#define bignum_madd_n25519_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_madd_n25519_alt)
#define bignum_mod_n25519 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_mod_n25519)
#define bignum_montinv_p256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_montinv_p256)
#define bignum_montinv_p384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_montinv_p384)
#define bignum_montmul_p384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_montmul_p384)
#define bignum_montmul_p384_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_montmul_p384_alt)
#define bignum_montsqr_p384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_montsqr_p384)
#define bignum_montsqr_p384_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_montsqr_p384_alt)
#define bignum_mul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_mul)
#define bignum_mul_p521 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_mul_p521)
#define bignum_mul_p521_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_mul_p521_alt)
#define bignum_neg_p25519 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_neg_p25519)
#define bignum_neg_p384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_neg_p384)
#define bignum_neg_p521 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_neg_p521)
#define bignum_nonzero_6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_nonzero_6)
#define bignum_optsub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_optsub)
#define bignum_sqr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_sqr)
#define bignum_sqr_p521 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_sqr_p521)
#define bignum_sqr_p521_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_sqr_p521_alt)
#define bignum_sub_p384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_sub_p384)
#define bignum_sub_p521 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_sub_p521)
#define bignum_tolebytes_6 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_tolebytes_6)
#define bignum_tolebytes_p521 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_tolebytes_p521)
#define bignum_tomont_p384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_tomont_p384)
#define bignum_tomont_p384_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bignum_tomont_p384_alt)
#define bio_clear_socket_error BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bio_clear_socket_error)
#define bio_errno_should_retry BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bio_errno_should_retry)
#define bio_ip_and_port_to_socket_and_addr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bio_ip_and_port_to_socket_and_addr)
#define bio_sock_error BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bio_sock_error)
#define bio_socket_nbio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bio_socket_nbio)
#define bio_socket_should_retry BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bio_socket_should_retry)
#define bn_abs_sub_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_abs_sub_consttime)
#define bn_add_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_add_words)
#define bn_assert_fits_in_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_assert_fits_in_bytes)
#define bn_big_endian_to_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_big_endian_to_words)
#define bn_copy_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_copy_words)
#define bn_div_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_div_consttime)
#define bn_div_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_div_words)
#define bn_expand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_expand)
#define bn_fits_in_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_fits_in_words)
#define bn_from_montgomery_small BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_from_montgomery_small)
#define bn_gather5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_gather5)
#define bn_in_range_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_in_range_words)
#define bn_is_bit_set_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_is_bit_set_words)
#define bn_is_relatively_prime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_is_relatively_prime)
#define bn_jacobi BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_jacobi)
#define bn_lcm_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_lcm_consttime)
#define bn_less_than_montgomery_R BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_less_than_montgomery_R)
#define bn_less_than_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_less_than_words)
#define bn_little_endian_to_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_little_endian_to_words)
#define bn_miller_rabin_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_miller_rabin_init)
#define bn_miller_rabin_iteration BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_miller_rabin_iteration)
#define bn_minimal_width BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_minimal_width)
#define bn_mod_add_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_add_consttime)
#define bn_mod_add_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_add_words)
#define bn_mod_exp_mont_small BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_exp_mont_small)
#define bn_mod_inverse0_prime_mont_small BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_inverse0_prime_mont_small)
#define bn_mod_inverse_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_inverse_consttime)
#define bn_mod_inverse_prime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_inverse_prime)
#define bn_mod_inverse_secret_prime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_inverse_secret_prime)
#define bn_mod_lshift1_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_lshift1_consttime)
#define bn_mod_lshift_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_lshift_consttime)
#define bn_mod_mul_montgomery_small BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_mul_montgomery_small)
#define bn_mod_sub_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_sub_consttime)
#define bn_mod_sub_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_sub_words)
#define bn_mod_u16_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mod_u16_consttime)
#define bn_mont_ctx_cleanup BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mont_ctx_cleanup)
#define bn_mont_ctx_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mont_ctx_init)
#define bn_mont_ctx_set_RR_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mont_ctx_set_RR_consttime)
#define bn_mont_n0 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mont_n0)
#define bn_mul4x_mont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul4x_mont)
#define bn_mul8x_mont_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul8x_mont_neon)
#define bn_mul_add_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul_add_words)
#define bn_mul_comba4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul_comba4)
#define bn_mul_comba8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul_comba8)
#define bn_mul_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul_consttime)
#define bn_mul_mont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul_mont)
#define bn_mul_mont_gather5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul_mont_gather5)
#define bn_mul_mont_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul_mont_nohw)
#define bn_mul_small BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul_small)
#define bn_mul_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mul_words)
#define bn_mulx4x_mont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_mulx4x_mont)
#define bn_odd_number_is_obviously_composite BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_odd_number_is_obviously_composite)
#define bn_one_to_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_one_to_montgomery)
#define bn_power5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_power5)
#define bn_rand_range_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_rand_range_words)
#define bn_rand_secret_range BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_rand_secret_range)
#define bn_reduce_once BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_reduce_once)
#define bn_reduce_once_in_place BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_reduce_once_in_place)
#define bn_resize_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_resize_words)
#define bn_rshift1_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_rshift1_words)
#define bn_rshift_secret_shift BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_rshift_secret_shift)
#define bn_rshift_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_rshift_words)
#define bn_scatter5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_scatter5)
#define bn_select_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_select_words)
#define bn_set_minimal_width BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_set_minimal_width)
#define bn_set_static_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_set_static_words)
#define bn_set_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_set_words)
#define bn_sqr8x_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_sqr8x_internal)
#define bn_sqr8x_mont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_sqr8x_mont)
#define bn_sqr_comba4 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_sqr_comba4)
#define bn_sqr_comba8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_sqr_comba8)
#define bn_sqr_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_sqr_consttime)
#define bn_sqr_small BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_sqr_small)
#define bn_sqr_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_sqr_words)
#define bn_sqrx8x_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_sqrx8x_internal)
#define bn_sub_words BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_sub_words)
#define bn_to_montgomery_small BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_to_montgomery_small)
#define bn_uadd_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_uadd_consttime)
#define bn_usub_consttime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_usub_consttime)
#define bn_wexpand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_wexpand)
#define bn_words_to_big_endian BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_words_to_big_endian)
#define bn_words_to_little_endian BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bn_words_to_little_endian)
#define boringssl_self_test_hmac_sha256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, boringssl_self_test_hmac_sha256)
#define boringssl_self_test_sha256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, boringssl_self_test_sha256)
#define bsaes_cbc_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bsaes_cbc_encrypt)
#define bsaes_ctr32_encrypt_blocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, bsaes_ctr32_encrypt_blocks)
#define c2i_ASN1_BIT_STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, c2i_ASN1_BIT_STRING)
#define c2i_ASN1_INTEGER BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, c2i_ASN1_INTEGER)
#define c2i_ASN1_OBJECT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, c2i_ASN1_OBJECT)
#define cbb_add_latin1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, cbb_add_latin1)
#define cbb_add_ucs2_be BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, cbb_add_ucs2_be)
#define cbb_add_utf32_be BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, cbb_add_utf32_be)
#define cbb_add_utf8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, cbb_add_utf8)
#define cbb_get_utf8_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, cbb_get_utf8_len)
#define cbs_get_any_asn1_element BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, cbs_get_any_asn1_element)
#define cbs_get_latin1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, cbs_get_latin1)
#define cbs_get_ucs2_be BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, cbs_get_ucs2_be)
#define cbs_get_utf32_be BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, cbs_get_utf32_be)
#define cbs_get_utf8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, cbs_get_utf8)
#define chacha20_poly1305_open BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, chacha20_poly1305_open)
#define chacha20_poly1305_seal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, chacha20_poly1305_seal)
#define crypto_gcm_avx512_enabled BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, crypto_gcm_avx512_enabled)
#define crypto_gcm_clmul_enabled BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, crypto_gcm_clmul_enabled)
#define curve25519_x25519 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, curve25519_x25519)
#define curve25519_x25519_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, curve25519_x25519_alt)
#define curve25519_x25519_byte BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, curve25519_x25519_byte)
#define curve25519_x25519_byte_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, curve25519_x25519_byte_alt)
#define curve25519_x25519base BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, curve25519_x25519base)
#define curve25519_x25519base_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, curve25519_x25519base_alt)
#define curve25519_x25519base_byte BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, curve25519_x25519base_byte)
#define curve25519_x25519base_byte_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, curve25519_x25519base_byte_alt)
#define d2i_ASN1_BIT_STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_BIT_STRING)
#define d2i_ASN1_BMPSTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_BMPSTRING)
#define d2i_ASN1_BOOLEAN BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_BOOLEAN)
#define d2i_ASN1_ENUMERATED BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_ENUMERATED)
#define d2i_ASN1_GENERALIZEDTIME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_GENERALIZEDTIME)
#define d2i_ASN1_GENERALSTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_GENERALSTRING)
#define d2i_ASN1_IA5STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_IA5STRING)
#define d2i_ASN1_INTEGER BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_INTEGER)
#define d2i_ASN1_NULL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_NULL)
#define d2i_ASN1_OBJECT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_OBJECT)
#define d2i_ASN1_OCTET_STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_OCTET_STRING)
#define d2i_ASN1_PRINTABLE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_PRINTABLE)
#define d2i_ASN1_PRINTABLESTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_PRINTABLESTRING)
#define d2i_ASN1_SEQUENCE_ANY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_SEQUENCE_ANY)
#define d2i_ASN1_SET_ANY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_SET_ANY)
#define d2i_ASN1_T61STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_T61STRING)
#define d2i_ASN1_TIME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_TIME)
#define d2i_ASN1_TYPE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_TYPE)
#define d2i_ASN1_UNIVERSALSTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_UNIVERSALSTRING)
#define d2i_ASN1_UTCTIME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_UTCTIME)
#define d2i_ASN1_UTF8STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_UTF8STRING)
#define d2i_ASN1_VISIBLESTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ASN1_VISIBLESTRING)
#define d2i_AUTHORITY_INFO_ACCESS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_AUTHORITY_INFO_ACCESS)
#define d2i_AUTHORITY_KEYID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_AUTHORITY_KEYID)
#define d2i_AutoPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_AutoPrivateKey)
#define d2i_BASIC_CONSTRAINTS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_BASIC_CONSTRAINTS)
#define d2i_CERTIFICATEPOLICIES BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_CERTIFICATEPOLICIES)
#define d2i_CRL_DIST_POINTS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_CRL_DIST_POINTS)
#define d2i_DHparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DHparams)
#define d2i_DHparams_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DHparams_bio)
#define d2i_DIRECTORYSTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DIRECTORYSTRING)
#define d2i_DISPLAYTEXT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DISPLAYTEXT)
#define d2i_DSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DSAPrivateKey)
#define d2i_DSAPrivateKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DSAPrivateKey_bio)
#define d2i_DSAPrivateKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DSAPrivateKey_fp)
#define d2i_DSAPublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DSAPublicKey)
#define d2i_DSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DSA_PUBKEY)
#define d2i_DSA_PUBKEY_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DSA_PUBKEY_bio)
#define d2i_DSA_PUBKEY_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DSA_PUBKEY_fp)
#define d2i_DSA_SIG BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DSA_SIG)
#define d2i_DSAparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_DSAparams)
#define d2i_ECDSA_SIG BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ECDSA_SIG)
#define d2i_ECPKParameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ECPKParameters)
#define d2i_ECPKParameters_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ECPKParameters_bio)
#define d2i_ECParameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ECParameters)
#define d2i_ECPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ECPrivateKey)
#define d2i_ECPrivateKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ECPrivateKey_bio)
#define d2i_ECPrivateKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ECPrivateKey_fp)
#define d2i_EC_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_EC_PUBKEY)
#define d2i_EC_PUBKEY_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_EC_PUBKEY_bio)
#define d2i_EC_PUBKEY_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_EC_PUBKEY_fp)
#define d2i_EXTENDED_KEY_USAGE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_EXTENDED_KEY_USAGE)
#define d2i_GENERAL_NAME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_GENERAL_NAME)
#define d2i_GENERAL_NAMES BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_GENERAL_NAMES)
#define d2i_ISSUING_DIST_POINT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_ISSUING_DIST_POINT)
#define d2i_NETSCAPE_SPKAC BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_NETSCAPE_SPKAC)
#define d2i_NETSCAPE_SPKI BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_NETSCAPE_SPKI)
#define d2i_OCSP_BASICRESP BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_BASICRESP)
#define d2i_OCSP_CERTID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_CERTID)
#define d2i_OCSP_ONEREQ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_ONEREQ)
#define d2i_OCSP_REQINFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_REQINFO)
#define d2i_OCSP_REQUEST BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_REQUEST)
#define d2i_OCSP_REQUEST_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_REQUEST_bio)
#define d2i_OCSP_RESPBYTES BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_RESPBYTES)
#define d2i_OCSP_RESPDATA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_RESPDATA)
#define d2i_OCSP_RESPONSE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_RESPONSE)
#define d2i_OCSP_RESPONSE_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_RESPONSE_bio)
#define d2i_OCSP_REVOKEDINFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_REVOKEDINFO)
#define d2i_OCSP_SIGNATURE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_SIGNATURE)
#define d2i_OCSP_SINGLERESP BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_OCSP_SINGLERESP)
#define d2i_PKCS12 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS12)
#define d2i_PKCS12_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS12_bio)
#define d2i_PKCS12_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS12_fp)
#define d2i_PKCS7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7)
#define d2i_PKCS7_DIGEST BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7_DIGEST)
#define d2i_PKCS7_ENCRYPT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7_ENCRYPT)
#define d2i_PKCS7_ENC_CONTENT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7_ENC_CONTENT)
#define d2i_PKCS7_ENVELOPE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7_ENVELOPE)
#define d2i_PKCS7_ISSUER_AND_SERIAL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7_ISSUER_AND_SERIAL)
#define d2i_PKCS7_RECIP_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7_RECIP_INFO)
#define d2i_PKCS7_SIGNED BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7_SIGNED)
#define d2i_PKCS7_SIGNER_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7_SIGNER_INFO)
#define d2i_PKCS7_SIGN_ENVELOPE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7_SIGN_ENVELOPE)
#define d2i_PKCS7_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS7_bio)
#define d2i_PKCS8PrivateKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS8PrivateKey_bio)
#define d2i_PKCS8PrivateKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS8PrivateKey_fp)
#define d2i_PKCS8_PRIV_KEY_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS8_PRIV_KEY_INFO)
#define d2i_PKCS8_PRIV_KEY_INFO_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS8_PRIV_KEY_INFO_bio)
#define d2i_PKCS8_PRIV_KEY_INFO_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS8_PRIV_KEY_INFO_fp)
#define d2i_PKCS8_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS8_bio)
#define d2i_PKCS8_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PKCS8_fp)
#define d2i_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PUBKEY)
#define d2i_PUBKEY_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PUBKEY_bio)
#define d2i_PUBKEY_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PUBKEY_fp)
#define d2i_PrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PrivateKey)
#define d2i_PrivateKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PrivateKey_bio)
#define d2i_PrivateKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PrivateKey_fp)
#define d2i_PublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_PublicKey)
#define d2i_RSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_RSAPrivateKey)
#define d2i_RSAPrivateKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_RSAPrivateKey_bio)
#define d2i_RSAPrivateKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_RSAPrivateKey_fp)
#define d2i_RSAPublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_RSAPublicKey)
#define d2i_RSAPublicKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_RSAPublicKey_bio)
#define d2i_RSAPublicKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_RSAPublicKey_fp)
#define d2i_RSA_PSS_PARAMS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_RSA_PSS_PARAMS)
#define d2i_RSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_RSA_PUBKEY)
#define d2i_RSA_PUBKEY_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_RSA_PUBKEY_bio)
#define d2i_RSA_PUBKEY_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_RSA_PUBKEY_fp)
#define d2i_X509 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509)
#define d2i_X509_ALGOR BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_ALGOR)
#define d2i_X509_ATTRIBUTE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_ATTRIBUTE)
#define d2i_X509_AUX BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_AUX)
#define d2i_X509_CERT_AUX BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_CERT_AUX)
#define d2i_X509_CINF BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_CINF)
#define d2i_X509_CRL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_CRL)
#define d2i_X509_CRL_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_CRL_INFO)
#define d2i_X509_CRL_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_CRL_bio)
#define d2i_X509_CRL_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_CRL_fp)
#define d2i_X509_EXTENSION BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_EXTENSION)
#define d2i_X509_EXTENSIONS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_EXTENSIONS)
#define d2i_X509_NAME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_NAME)
#define d2i_X509_NAME_ENTRY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_NAME_ENTRY)
#define d2i_X509_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_PUBKEY)
#define d2i_X509_REQ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_REQ)
#define d2i_X509_REQ_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_REQ_INFO)
#define d2i_X509_REQ_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_REQ_bio)
#define d2i_X509_REQ_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_REQ_fp)
#define d2i_X509_REVOKED BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_REVOKED)
#define d2i_X509_SIG BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_SIG)
#define d2i_X509_VAL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_VAL)
#define d2i_X509_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_bio)
#define d2i_X509_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, d2i_X509_fp)
#define dh_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, dh_asn1_meth)
#define dh_check_params_fast BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, dh_check_params_fast)
#define dh_compute_key_padded_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, dh_compute_key_padded_no_self_test)
#define dh_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, dh_pkey_meth)
#define dsa_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, dsa_asn1_meth)
#define dsa_check_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, dsa_check_key)
#define dsa_internal_paramgen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, dsa_internal_paramgen)
#define dsa_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, dsa_pkey_meth)
#define dummy_func_for_constructor BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, dummy_func_for_constructor)
#define ec_GFp_mont_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_add)
#define ec_GFp_mont_dbl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_dbl)
#define ec_GFp_mont_felem_exp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_felem_exp)
#define ec_GFp_mont_felem_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_felem_from_bytes)
#define ec_GFp_mont_felem_mul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_felem_mul)
#define ec_GFp_mont_felem_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_felem_reduce)
#define ec_GFp_mont_felem_sqr BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_felem_sqr)
#define ec_GFp_mont_felem_to_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_felem_to_bytes)
#define ec_GFp_mont_init_precomp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_init_precomp)
#define ec_GFp_mont_mul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_mul)
#define ec_GFp_mont_mul_base BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_mul_base)
#define ec_GFp_mont_mul_batch BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_mul_batch)
#define ec_GFp_mont_mul_precomp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_mul_precomp)
#define ec_GFp_mont_mul_public_batch BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_mont_mul_public_batch)
#define ec_GFp_nistp_recode_scalar_bits BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_nistp_recode_scalar_bits)
#define ec_GFp_simple_cmp_x_coordinate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_cmp_x_coordinate)
#define ec_GFp_simple_felem_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_felem_from_bytes)
#define ec_GFp_simple_felem_to_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_felem_to_bytes)
#define ec_GFp_simple_group_get_curve BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_group_get_curve)
#define ec_GFp_simple_group_set_curve BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_group_set_curve)
#define ec_GFp_simple_invert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_invert)
#define ec_GFp_simple_is_at_infinity BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_is_at_infinity)
#define ec_GFp_simple_is_on_curve BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_is_on_curve)
#define ec_GFp_simple_point_copy BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_point_copy)
#define ec_GFp_simple_point_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_point_init)
#define ec_GFp_simple_point_set_to_infinity BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_point_set_to_infinity)
#define ec_GFp_simple_points_equal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_GFp_simple_points_equal)
#define ec_affine_jacobian_equal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_affine_jacobian_equal)
#define ec_affine_select BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_affine_select)
#define ec_affine_to_jacobian BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_affine_to_jacobian)
#define ec_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_asn1_meth)
#define ec_bignum_to_felem BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_bignum_to_felem)
#define ec_bignum_to_scalar BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_bignum_to_scalar)
#define ec_cmp_x_coordinate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_cmp_x_coordinate)
#define ec_compute_wNAF BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_compute_wNAF)
#define ec_felem_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_felem_add)
#define ec_felem_equal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_felem_equal)
#define ec_felem_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_felem_from_bytes)
#define ec_felem_neg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_felem_neg)
#define ec_felem_non_zero_mask BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_felem_non_zero_mask)
#define ec_felem_one BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_felem_one)
#define ec_felem_select BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_felem_select)
#define ec_felem_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_felem_sub)
#define ec_felem_to_bignum BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_felem_to_bignum)
#define ec_felem_to_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_felem_to_bytes)
#define ec_get_x_coordinate_as_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_get_x_coordinate_as_bytes)
#define ec_get_x_coordinate_as_scalar BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_get_x_coordinate_as_scalar)
#define ec_hash_to_curve_p256_xmd_sha256_sswu BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_hash_to_curve_p256_xmd_sha256_sswu)
#define ec_hash_to_curve_p384_xmd_sha384_sswu BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_hash_to_curve_p384_xmd_sha384_sswu)
#define ec_hash_to_curve_p384_xmd_sha512_sswu_draft07 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_hash_to_curve_p384_xmd_sha512_sswu_draft07)
#define ec_hash_to_scalar_p384_xmd_sha384 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_hash_to_scalar_p384_xmd_sha384)
#define ec_hash_to_scalar_p384_xmd_sha512_draft07 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_hash_to_scalar_p384_xmd_sha512_draft07)
#define ec_init_precomp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_init_precomp)
#define ec_jacobian_to_affine BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_jacobian_to_affine)
#define ec_jacobian_to_affine_batch BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_jacobian_to_affine_batch)
#define ec_nistp_coordinates_to_point BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_nistp_coordinates_to_point)
#define ec_nistp_point_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_nistp_point_add)
#define ec_nistp_point_double BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_nistp_point_double)
#define ec_nistp_point_to_coordinates BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_nistp_point_to_coordinates)
#define ec_nistp_scalar_mul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_nistp_scalar_mul)
#define ec_nistp_scalar_mul_base BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_nistp_scalar_mul_base)
#define ec_nistp_scalar_mul_public BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_nistp_scalar_mul_public)
#define ec_point_byte_len BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_byte_len)
#define ec_point_from_uncompressed BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_from_uncompressed)
#define ec_point_mul_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_mul_no_self_test)
#define ec_point_mul_scalar BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_mul_scalar)
#define ec_point_mul_scalar_base BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_mul_scalar_base)
#define ec_point_mul_scalar_batch BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_mul_scalar_batch)
#define ec_point_mul_scalar_precomp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_mul_scalar_precomp)
#define ec_point_mul_scalar_public BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_mul_scalar_public)
#define ec_point_mul_scalar_public_batch BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_mul_scalar_public_batch)
#define ec_point_select BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_select)
#define ec_point_set_affine_coordinates BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_set_affine_coordinates)
#define ec_point_to_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_point_to_bytes)
#define ec_precomp_select BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_precomp_select)
#define ec_random_nonzero_scalar BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_random_nonzero_scalar)
#define ec_scalar_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_add)
#define ec_scalar_equal_vartime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_equal_vartime)
#define ec_scalar_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_from_bytes)
#define ec_scalar_from_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_from_montgomery)
#define ec_scalar_inv0_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_inv0_montgomery)
#define ec_scalar_is_zero BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_is_zero)
#define ec_scalar_mul_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_mul_montgomery)
#define ec_scalar_neg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_neg)
#define ec_scalar_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_reduce)
#define ec_scalar_select BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_select)
#define ec_scalar_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_sub)
#define ec_scalar_to_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_to_bytes)
#define ec_scalar_to_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_to_montgomery)
#define ec_scalar_to_montgomery_inv_vartime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_scalar_to_montgomery_inv_vartime)
#define ec_set_to_safe_point BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_set_to_safe_point)
#define ec_simple_scalar_inv0_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_simple_scalar_inv0_montgomery)
#define ec_simple_scalar_to_montgomery_inv_vartime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ec_simple_scalar_to_montgomery_inv_vartime)
#define ecdsa_digestsign_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecdsa_digestsign_no_self_test)
#define ecdsa_digestverify_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecdsa_digestverify_no_self_test)
#define ecdsa_do_verify_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecdsa_do_verify_no_self_test)
#define ecdsa_sign_with_nonce_for_known_answer_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecdsa_sign_with_nonce_for_known_answer_test)
#define ecp_nistz256_avx2_select_w7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_avx2_select_w7)
#define ecp_nistz256_div_by_2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_div_by_2)
#define ecp_nistz256_mul_by_2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_mul_by_2)
#define ecp_nistz256_mul_by_3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_mul_by_3)
#define ecp_nistz256_mul_mont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_mul_mont)
#define ecp_nistz256_neg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_neg)
#define ecp_nistz256_ord_mul_mont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_ord_mul_mont)
#define ecp_nistz256_ord_sqr_mont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_ord_sqr_mont)
#define ecp_nistz256_point_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_point_add)
#define ecp_nistz256_point_add_affine BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_point_add_affine)
#define ecp_nistz256_point_double BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_point_double)
#define ecp_nistz256_select_w5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_select_w5)
#define ecp_nistz256_select_w7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_select_w7)
#define ecp_nistz256_sqr_mont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_sqr_mont)
#define ecp_nistz256_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ecp_nistz256_sub)
#define ed25519_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_asn1_meth)
#define ed25519_check_public_key_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_check_public_key_nohw)
#define ed25519_check_public_key_s2n_bignum BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_check_public_key_s2n_bignum)
#define ed25519_public_key_from_hashed_seed_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_public_key_from_hashed_seed_nohw)
#define ed25519_public_key_from_hashed_seed_s2n_bignum BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_public_key_from_hashed_seed_s2n_bignum)
#define ed25519_sha512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_sha512)
#define ed25519_sign_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_sign_internal)
#define ed25519_sign_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_sign_nohw)
#define ed25519_sign_s2n_bignum BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_sign_s2n_bignum)
#define ed25519_verify_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_verify_internal)
#define ed25519_verify_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_verify_nohw)
#define ed25519_verify_s2n_bignum BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519_verify_s2n_bignum)
#define ed25519ph_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ed25519ph_asn1_meth)
#define edwards25519_decode BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, edwards25519_decode)
#define edwards25519_decode_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, edwards25519_decode_alt)
#define edwards25519_encode BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, edwards25519_encode)
#define edwards25519_scalarmulbase BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, edwards25519_scalarmulbase)
#define edwards25519_scalarmulbase_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, edwards25519_scalarmulbase_alt)
#define edwards25519_scalarmuldouble BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, edwards25519_scalarmuldouble)
#define edwards25519_scalarmuldouble_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, edwards25519_scalarmuldouble_alt)
#define evp_pkey_set_cb_translate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, evp_pkey_set_cb_translate)
#define evp_pkey_set_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, evp_pkey_set_method)
#define extract_multiplier_2x20_win5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, extract_multiplier_2x20_win5)
#define extract_multiplier_2x30_win5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, extract_multiplier_2x30_win5)
#define extract_multiplier_2x40_win5 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, extract_multiplier_2x40_win5)
#define gcm_ghash_avx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_ghash_avx)
#define gcm_ghash_avx512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_ghash_avx512)
#define gcm_ghash_clmul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_ghash_clmul)
#define gcm_ghash_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_ghash_neon)
#define gcm_ghash_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_ghash_nohw)
#define gcm_ghash_p8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_ghash_p8)
#define gcm_ghash_ssse3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_ghash_ssse3)
#define gcm_ghash_v8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_ghash_v8)
#define gcm_gmult_avx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_gmult_avx)
#define gcm_gmult_avx512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_gmult_avx512)
#define gcm_gmult_clmul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_gmult_clmul)
#define gcm_gmult_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_gmult_neon)
#define gcm_gmult_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_gmult_nohw)
#define gcm_gmult_p8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_gmult_p8)
#define gcm_gmult_ssse3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_gmult_ssse3)
#define gcm_gmult_v8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_gmult_v8)
#define gcm_init_avx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_init_avx)
#define gcm_init_avx512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_init_avx512)
#define gcm_init_clmul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_init_clmul)
#define gcm_init_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_init_neon)
#define gcm_init_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_init_nohw)
#define gcm_init_p8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_init_p8)
#define gcm_init_ssse3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_init_ssse3)
#define gcm_init_v8 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_init_v8)
#define gcm_setiv_avx512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, gcm_setiv_avx512)
#define get_legacy_kem_kyber1024_r3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, get_legacy_kem_kyber1024_r3)
#define get_legacy_kem_kyber512_r3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, get_legacy_kem_kyber512_r3)
#define get_legacy_kem_kyber768_r3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, get_legacy_kem_kyber768_r3)
#define handle_cpu_env BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, handle_cpu_env)
#define hmac_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, hmac_asn1_meth)
#define i2a_ASN1_ENUMERATED BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2a_ASN1_ENUMERATED)
#define i2a_ASN1_INTEGER BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2a_ASN1_INTEGER)
#define i2a_ASN1_OBJECT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2a_ASN1_OBJECT)
#define i2a_ASN1_STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2a_ASN1_STRING)
#define i2c_ASN1_BIT_STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2c_ASN1_BIT_STRING)
#define i2c_ASN1_INTEGER BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2c_ASN1_INTEGER)
#define i2d_ASN1_BIT_STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_BIT_STRING)
#define i2d_ASN1_BMPSTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_BMPSTRING)
#define i2d_ASN1_BOOLEAN BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_BOOLEAN)
#define i2d_ASN1_ENUMERATED BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_ENUMERATED)
#define i2d_ASN1_GENERALIZEDTIME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_GENERALIZEDTIME)
#define i2d_ASN1_GENERALSTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_GENERALSTRING)
#define i2d_ASN1_IA5STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_IA5STRING)
#define i2d_ASN1_INTEGER BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_INTEGER)
#define i2d_ASN1_NULL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_NULL)
#define i2d_ASN1_OBJECT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_OBJECT)
#define i2d_ASN1_OCTET_STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_OCTET_STRING)
#define i2d_ASN1_PRINTABLE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_PRINTABLE)
#define i2d_ASN1_PRINTABLESTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_PRINTABLESTRING)
#define i2d_ASN1_SEQUENCE_ANY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_SEQUENCE_ANY)
#define i2d_ASN1_SET_ANY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_SET_ANY)
#define i2d_ASN1_T61STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_T61STRING)
#define i2d_ASN1_TIME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_TIME)
#define i2d_ASN1_TYPE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_TYPE)
#define i2d_ASN1_UNIVERSALSTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_UNIVERSALSTRING)
#define i2d_ASN1_UTCTIME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_UTCTIME)
#define i2d_ASN1_UTF8STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_UTF8STRING)
#define i2d_ASN1_VISIBLESTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ASN1_VISIBLESTRING)
#define i2d_AUTHORITY_INFO_ACCESS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_AUTHORITY_INFO_ACCESS)
#define i2d_AUTHORITY_KEYID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_AUTHORITY_KEYID)
#define i2d_BASIC_CONSTRAINTS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_BASIC_CONSTRAINTS)
#define i2d_CERTIFICATEPOLICIES BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_CERTIFICATEPOLICIES)
#define i2d_CRL_DIST_POINTS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_CRL_DIST_POINTS)
#define i2d_DHparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DHparams)
#define i2d_DHparams_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DHparams_bio)
#define i2d_DIRECTORYSTRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DIRECTORYSTRING)
#define i2d_DISPLAYTEXT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DISPLAYTEXT)
#define i2d_DSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DSAPrivateKey)
#define i2d_DSAPrivateKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DSAPrivateKey_bio)
#define i2d_DSAPrivateKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DSAPrivateKey_fp)
#define i2d_DSAPublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DSAPublicKey)
#define i2d_DSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DSA_PUBKEY)
#define i2d_DSA_PUBKEY_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DSA_PUBKEY_bio)
#define i2d_DSA_PUBKEY_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DSA_PUBKEY_fp)
#define i2d_DSA_SIG BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DSA_SIG)
#define i2d_DSAparams BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_DSAparams)
#define i2d_ECDSA_SIG BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ECDSA_SIG)
#define i2d_ECPKParameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ECPKParameters)
#define i2d_ECPKParameters_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ECPKParameters_bio)
#define i2d_ECParameters BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ECParameters)
#define i2d_ECPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ECPrivateKey)
#define i2d_ECPrivateKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ECPrivateKey_bio)
#define i2d_ECPrivateKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ECPrivateKey_fp)
#define i2d_EC_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_EC_PUBKEY)
#define i2d_EC_PUBKEY_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_EC_PUBKEY_bio)
#define i2d_EC_PUBKEY_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_EC_PUBKEY_fp)
#define i2d_EXTENDED_KEY_USAGE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_EXTENDED_KEY_USAGE)
#define i2d_GENERAL_NAME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_GENERAL_NAME)
#define i2d_GENERAL_NAMES BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_GENERAL_NAMES)
#define i2d_ISSUING_DIST_POINT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_ISSUING_DIST_POINT)
#define i2d_NETSCAPE_SPKAC BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_NETSCAPE_SPKAC)
#define i2d_NETSCAPE_SPKI BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_NETSCAPE_SPKI)
#define i2d_OCSP_BASICRESP BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_BASICRESP)
#define i2d_OCSP_CERTID BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_CERTID)
#define i2d_OCSP_ONEREQ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_ONEREQ)
#define i2d_OCSP_REQINFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_REQINFO)
#define i2d_OCSP_REQUEST BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_REQUEST)
#define i2d_OCSP_REQUEST_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_REQUEST_bio)
#define i2d_OCSP_RESPBYTES BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_RESPBYTES)
#define i2d_OCSP_RESPDATA BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_RESPDATA)
#define i2d_OCSP_RESPONSE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_RESPONSE)
#define i2d_OCSP_RESPONSE_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_RESPONSE_bio)
#define i2d_OCSP_REVOKEDINFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_REVOKEDINFO)
#define i2d_OCSP_SIGNATURE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_SIGNATURE)
#define i2d_OCSP_SINGLERESP BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_OCSP_SINGLERESP)
#define i2d_PKCS12 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS12)
#define i2d_PKCS12_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS12_bio)
#define i2d_PKCS12_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS12_fp)
#define i2d_PKCS7 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7)
#define i2d_PKCS7_DIGEST BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7_DIGEST)
#define i2d_PKCS7_ENCRYPT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7_ENCRYPT)
#define i2d_PKCS7_ENC_CONTENT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7_ENC_CONTENT)
#define i2d_PKCS7_ENVELOPE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7_ENVELOPE)
#define i2d_PKCS7_ISSUER_AND_SERIAL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7_ISSUER_AND_SERIAL)
#define i2d_PKCS7_RECIP_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7_RECIP_INFO)
#define i2d_PKCS7_SIGNED BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7_SIGNED)
#define i2d_PKCS7_SIGNER_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7_SIGNER_INFO)
#define i2d_PKCS7_SIGN_ENVELOPE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7_SIGN_ENVELOPE)
#define i2d_PKCS7_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS7_bio)
#define i2d_PKCS8PrivateKeyInfo_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8PrivateKeyInfo_bio)
#define i2d_PKCS8PrivateKeyInfo_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8PrivateKeyInfo_fp)
#define i2d_PKCS8PrivateKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8PrivateKey_bio)
#define i2d_PKCS8PrivateKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8PrivateKey_fp)
#define i2d_PKCS8PrivateKey_nid_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8PrivateKey_nid_bio)
#define i2d_PKCS8PrivateKey_nid_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8PrivateKey_nid_fp)
#define i2d_PKCS8_PRIV_KEY_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8_PRIV_KEY_INFO)
#define i2d_PKCS8_PRIV_KEY_INFO_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8_PRIV_KEY_INFO_bio)
#define i2d_PKCS8_PRIV_KEY_INFO_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8_PRIV_KEY_INFO_fp)
#define i2d_PKCS8_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8_bio)
#define i2d_PKCS8_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PKCS8_fp)
#define i2d_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PUBKEY)
#define i2d_PUBKEY_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PUBKEY_bio)
#define i2d_PUBKEY_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PUBKEY_fp)
#define i2d_PrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PrivateKey)
#define i2d_PrivateKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PrivateKey_bio)
#define i2d_PrivateKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PrivateKey_fp)
#define i2d_PublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_PublicKey)
#define i2d_RSAPrivateKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_RSAPrivateKey)
#define i2d_RSAPrivateKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_RSAPrivateKey_bio)
#define i2d_RSAPrivateKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_RSAPrivateKey_fp)
#define i2d_RSAPublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_RSAPublicKey)
#define i2d_RSAPublicKey_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_RSAPublicKey_bio)
#define i2d_RSAPublicKey_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_RSAPublicKey_fp)
#define i2d_RSA_PSS_PARAMS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_RSA_PSS_PARAMS)
#define i2d_RSA_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_RSA_PUBKEY)
#define i2d_RSA_PUBKEY_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_RSA_PUBKEY_bio)
#define i2d_RSA_PUBKEY_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_RSA_PUBKEY_fp)
#define i2d_X509 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509)
#define i2d_X509_ALGOR BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_ALGOR)
#define i2d_X509_ATTRIBUTE BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_ATTRIBUTE)
#define i2d_X509_AUX BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_AUX)
#define i2d_X509_CERT_AUX BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_CERT_AUX)
#define i2d_X509_CINF BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_CINF)
#define i2d_X509_CRL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_CRL)
#define i2d_X509_CRL_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_CRL_INFO)
#define i2d_X509_CRL_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_CRL_bio)
#define i2d_X509_CRL_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_CRL_fp)
#define i2d_X509_CRL_tbs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_CRL_tbs)
#define i2d_X509_EXTENSION BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_EXTENSION)
#define i2d_X509_EXTENSIONS BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_EXTENSIONS)
#define i2d_X509_NAME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_NAME)
#define i2d_X509_NAME_ENTRY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_NAME_ENTRY)
#define i2d_X509_PUBKEY BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_PUBKEY)
#define i2d_X509_REQ BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_REQ)
#define i2d_X509_REQ_INFO BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_REQ_INFO)
#define i2d_X509_REQ_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_REQ_bio)
#define i2d_X509_REQ_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_REQ_fp)
#define i2d_X509_REVOKED BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_REVOKED)
#define i2d_X509_SIG BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_SIG)
#define i2d_X509_VAL BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_VAL)
#define i2d_X509_bio BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_bio)
#define i2d_X509_fp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_fp)
#define i2d_X509_tbs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_X509_tbs)
#define i2d_re_X509_CRL_tbs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_re_X509_CRL_tbs)
#define i2d_re_X509_REQ_tbs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_re_X509_REQ_tbs)
#define i2d_re_X509_tbs BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2d_re_X509_tbs)
#define i2o_ECPublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2o_ECPublicKey)
#define i2s_ASN1_ENUMERATED BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2s_ASN1_ENUMERATED)
#define i2s_ASN1_INTEGER BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2s_ASN1_INTEGER)
#define i2s_ASN1_OCTET_STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2s_ASN1_OCTET_STRING)
#define i2t_ASN1_OBJECT BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2t_ASN1_OBJECT)
#define i2v_GENERAL_NAME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2v_GENERAL_NAME)
#define i2v_GENERAL_NAMES BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, i2v_GENERAL_NAMES)
#define is_fips_build BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, is_fips_build)
#define is_public_component_of_rsa_key_good BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, is_public_component_of_rsa_key_good)
#define kBoringSSLRSASqrtTwo BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, kBoringSSLRSASqrtTwo)
#define kBoringSSLRSASqrtTwoLen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, kBoringSSLRSASqrtTwoLen)
#define kOpenSSLReasonStringData BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, kOpenSSLReasonStringData)
#define kOpenSSLReasonValues BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, kOpenSSLReasonValues)
#define kOpenSSLReasonValuesLen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, kOpenSSLReasonValuesLen)
#define kem_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, kem_asn1_meth)
#define lh_doall_arg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, lh_doall_arg)
#define library_init_constructor BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, library_init_constructor)
#define md4_block_data_order BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, md4_block_data_order)
#define md5_block_asm_data_order BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, md5_block_asm_data_order)
#define ml_dsa_44_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_keypair)
#define ml_dsa_44_keypair_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_keypair_internal)
#define ml_dsa_44_keypair_internal_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_keypair_internal_no_self_test)
#define ml_dsa_44_pack_pk_from_sk BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_pack_pk_from_sk)
#define ml_dsa_44_params_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_params_init)
#define ml_dsa_44_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_sign)
#define ml_dsa_44_sign_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_sign_internal)
#define ml_dsa_44_sign_internal_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_sign_internal_no_self_test)
#define ml_dsa_44_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_verify)
#define ml_dsa_44_verify_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_verify_internal)
#define ml_dsa_44_verify_internal_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_44_verify_internal_no_self_test)
#define ml_dsa_65_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_65_keypair)
#define ml_dsa_65_keypair_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_65_keypair_internal)
#define ml_dsa_65_pack_pk_from_sk BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_65_pack_pk_from_sk)
#define ml_dsa_65_params_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_65_params_init)
#define ml_dsa_65_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_65_sign)
#define ml_dsa_65_sign_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_65_sign_internal)
#define ml_dsa_65_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_65_verify)
#define ml_dsa_65_verify_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_65_verify_internal)
#define ml_dsa_87_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_87_keypair)
#define ml_dsa_87_keypair_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_87_keypair_internal)
#define ml_dsa_87_pack_pk_from_sk BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_87_pack_pk_from_sk)
#define ml_dsa_87_params_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_87_params_init)
#define ml_dsa_87_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_87_sign)
#define ml_dsa_87_sign_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_87_sign_internal)
#define ml_dsa_87_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_87_verify)
#define ml_dsa_87_verify_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_87_verify_internal)
#define ml_dsa_caddq BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_caddq)
#define ml_dsa_decompose BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_decompose)
#define ml_dsa_extmu_44_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_44_sign)
#define ml_dsa_extmu_44_sign_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_44_sign_internal)
#define ml_dsa_extmu_44_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_44_verify)
#define ml_dsa_extmu_44_verify_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_44_verify_internal)
#define ml_dsa_extmu_65_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_65_sign)
#define ml_dsa_extmu_65_sign_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_65_sign_internal)
#define ml_dsa_extmu_65_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_65_verify)
#define ml_dsa_extmu_65_verify_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_65_verify_internal)
#define ml_dsa_extmu_87_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_87_sign)
#define ml_dsa_extmu_87_sign_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_87_sign_internal)
#define ml_dsa_extmu_87_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_87_verify)
#define ml_dsa_extmu_87_verify_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_87_verify_internal)
#define ml_dsa_extmu_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_extmu_sign)
#define ml_dsa_fqmul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_fqmul)
#define ml_dsa_freeze BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_freeze)
#define ml_dsa_invntt_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_invntt_tomont)
#define ml_dsa_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_keypair)
#define ml_dsa_keypair_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_keypair_internal)
#define ml_dsa_make_hint BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_make_hint)
#define ml_dsa_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_ntt)
#define ml_dsa_pack_pk BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_pack_pk)
#define ml_dsa_pack_pk_from_sk BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_pack_pk_from_sk)
#define ml_dsa_pack_sig BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_pack_sig)
#define ml_dsa_pack_sk BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_pack_sk)
#define ml_dsa_poly_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_add)
#define ml_dsa_poly_caddq BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_caddq)
#define ml_dsa_poly_challenge BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_challenge)
#define ml_dsa_poly_chknorm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_chknorm)
#define ml_dsa_poly_decompose BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_decompose)
#define ml_dsa_poly_invntt_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_invntt_tomont)
#define ml_dsa_poly_make_hint BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_make_hint)
#define ml_dsa_poly_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_ntt)
#define ml_dsa_poly_pointwise_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_pointwise_montgomery)
#define ml_dsa_poly_power2round BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_power2round)
#define ml_dsa_poly_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_reduce)
#define ml_dsa_poly_shiftl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_shiftl)
#define ml_dsa_poly_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_sub)
#define ml_dsa_poly_uniform BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_uniform)
#define ml_dsa_poly_uniform_eta BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_uniform_eta)
#define ml_dsa_poly_uniform_gamma1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_uniform_gamma1)
#define ml_dsa_poly_use_hint BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_poly_use_hint)
#define ml_dsa_polyeta_pack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyeta_pack)
#define ml_dsa_polyeta_unpack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyeta_unpack)
#define ml_dsa_polyt0_pack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyt0_pack)
#define ml_dsa_polyt0_unpack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyt0_unpack)
#define ml_dsa_polyt1_pack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyt1_pack)
#define ml_dsa_polyt1_unpack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyt1_unpack)
#define ml_dsa_polyvec_matrix_expand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvec_matrix_expand)
#define ml_dsa_polyvec_matrix_pointwise_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvec_matrix_pointwise_montgomery)
#define ml_dsa_polyveck_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_add)
#define ml_dsa_polyveck_caddq BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_caddq)
#define ml_dsa_polyveck_chknorm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_chknorm)
#define ml_dsa_polyveck_decompose BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_decompose)
#define ml_dsa_polyveck_invntt_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_invntt_tomont)
#define ml_dsa_polyveck_make_hint BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_make_hint)
#define ml_dsa_polyveck_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_ntt)
#define ml_dsa_polyveck_pack_w1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_pack_w1)
#define ml_dsa_polyveck_pointwise_poly_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_pointwise_poly_montgomery)
#define ml_dsa_polyveck_power2round BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_power2round)
#define ml_dsa_polyveck_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_reduce)
#define ml_dsa_polyveck_shiftl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_shiftl)
#define ml_dsa_polyveck_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_sub)
#define ml_dsa_polyveck_uniform_eta BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_uniform_eta)
#define ml_dsa_polyveck_use_hint BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyveck_use_hint)
#define ml_dsa_polyvecl_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvecl_add)
#define ml_dsa_polyvecl_chknorm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvecl_chknorm)
#define ml_dsa_polyvecl_invntt_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvecl_invntt_tomont)
#define ml_dsa_polyvecl_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvecl_ntt)
#define ml_dsa_polyvecl_pointwise_acc_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvecl_pointwise_acc_montgomery)
#define ml_dsa_polyvecl_pointwise_poly_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvecl_pointwise_poly_montgomery)
#define ml_dsa_polyvecl_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvecl_reduce)
#define ml_dsa_polyvecl_uniform_eta BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvecl_uniform_eta)
#define ml_dsa_polyvecl_uniform_gamma1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyvecl_uniform_gamma1)
#define ml_dsa_polyw1_pack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyw1_pack)
#define ml_dsa_polyz_pack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyz_pack)
#define ml_dsa_polyz_unpack BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_polyz_unpack)
#define ml_dsa_power2round BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_power2round)
#define ml_dsa_reduce32 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_reduce32)
#define ml_dsa_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_sign)
#define ml_dsa_sign_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_sign_internal)
#define ml_dsa_sign_message BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_sign_message)
#define ml_dsa_unpack_pk BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_unpack_pk)
#define ml_dsa_unpack_sig BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_unpack_sig)
#define ml_dsa_unpack_sk BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_unpack_sk)
#define ml_dsa_use_hint BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_use_hint)
#define ml_dsa_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_verify)
#define ml_dsa_verify_internal BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_verify_internal)
#define ml_dsa_verify_message BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_dsa_verify_message)
#define ml_kem_1024_decapsulate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_1024_decapsulate)
#define ml_kem_1024_encapsulate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_1024_encapsulate)
#define ml_kem_1024_encapsulate_deterministic BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_1024_encapsulate_deterministic)
#define ml_kem_1024_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_1024_keypair)
#define ml_kem_1024_keypair_deterministic BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_1024_keypair_deterministic)
#define ml_kem_512_decapsulate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_512_decapsulate)
#define ml_kem_512_decapsulate_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_512_decapsulate_no_self_test)
#define ml_kem_512_encapsulate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_512_encapsulate)
#define ml_kem_512_encapsulate_deterministic BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_512_encapsulate_deterministic)
#define ml_kem_512_encapsulate_deterministic_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_512_encapsulate_deterministic_no_self_test)
#define ml_kem_512_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_512_keypair)
#define ml_kem_512_keypair_deterministic BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_512_keypair_deterministic)
#define ml_kem_512_keypair_deterministic_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_512_keypair_deterministic_no_self_test)
#define ml_kem_768_decapsulate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_768_decapsulate)
#define ml_kem_768_encapsulate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_768_encapsulate)
#define ml_kem_768_encapsulate_deterministic BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_768_encapsulate_deterministic)
#define ml_kem_768_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_768_keypair)
#define ml_kem_768_keypair_deterministic BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, ml_kem_768_keypair_deterministic)
#define mlkem_ct_opt_blocker_u64 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, mlkem_ct_opt_blocker_u64)
#define o2i_ECPublicKey BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, o2i_ECPublicKey)
#define openssl_poly1305_neon2_addmulmod BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, openssl_poly1305_neon2_addmulmod)
#define openssl_poly1305_neon2_blocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, openssl_poly1305_neon2_blocks)
#define p256_methods BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p256_methods)
#define p256_montjscalarmul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p256_montjscalarmul)
#define p256_montjscalarmul_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p256_montjscalarmul_alt)
#define p384_methods BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p384_methods)
#define p384_montjdouble BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p384_montjdouble)
#define p384_montjdouble_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p384_montjdouble_alt)
#define p384_montjscalarmul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p384_montjscalarmul)
#define p384_montjscalarmul_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p384_montjscalarmul_alt)
#define p521_jdouble BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p521_jdouble)
#define p521_jdouble_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p521_jdouble_alt)
#define p521_jscalarmul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p521_jscalarmul)
#define p521_jscalarmul_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p521_jscalarmul_alt)
#define p521_methods BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p521_methods)
#define p_thread_callback_boringssl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, p_thread_callback_boringssl)
#define pkcs12_iterations_acceptable BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pkcs12_iterations_acceptable)
#define pkcs12_key_gen BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pkcs12_key_gen)
#define pkcs12_pbe_encrypt_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pkcs12_pbe_encrypt_init)
#define pkcs7_add_signed_data BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pkcs7_add_signed_data)
#define pkcs7_final BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pkcs7_final)
#define pkcs7_parse_header BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pkcs7_parse_header)
#define pkcs8_pbe_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pkcs8_pbe_decrypt)
#define pmbtoken_exp1_blind BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp1_blind)
#define pmbtoken_exp1_client_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp1_client_key_from_bytes)
#define pmbtoken_exp1_derive_key_from_secret BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp1_derive_key_from_secret)
#define pmbtoken_exp1_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp1_generate_key)
#define pmbtoken_exp1_get_h_for_testing BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp1_get_h_for_testing)
#define pmbtoken_exp1_issuer_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp1_issuer_key_from_bytes)
#define pmbtoken_exp1_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp1_read)
#define pmbtoken_exp1_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp1_sign)
#define pmbtoken_exp1_unblind BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp1_unblind)
#define pmbtoken_exp2_blind BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp2_blind)
#define pmbtoken_exp2_client_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp2_client_key_from_bytes)
#define pmbtoken_exp2_derive_key_from_secret BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp2_derive_key_from_secret)
#define pmbtoken_exp2_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp2_generate_key)
#define pmbtoken_exp2_get_h_for_testing BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp2_get_h_for_testing)
#define pmbtoken_exp2_issuer_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp2_issuer_key_from_bytes)
#define pmbtoken_exp2_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp2_read)
#define pmbtoken_exp2_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp2_sign)
#define pmbtoken_exp2_unblind BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_exp2_unblind)
#define pmbtoken_pst1_blind BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_pst1_blind)
#define pmbtoken_pst1_client_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_pst1_client_key_from_bytes)
#define pmbtoken_pst1_derive_key_from_secret BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_pst1_derive_key_from_secret)
#define pmbtoken_pst1_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_pst1_generate_key)
#define pmbtoken_pst1_get_h_for_testing BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_pst1_get_h_for_testing)
#define pmbtoken_pst1_issuer_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_pst1_issuer_key_from_bytes)
#define pmbtoken_pst1_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_pst1_read)
#define pmbtoken_pst1_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_pst1_sign)
#define pmbtoken_pst1_unblind BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pmbtoken_pst1_unblind)
#define poly_Rq_mul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, poly_Rq_mul)
#define pqcrystals_kyber1024_ref_barrett_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_barrett_reduce)
#define pqcrystals_kyber1024_ref_basemul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_basemul)
#define pqcrystals_kyber1024_ref_cmov BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_cmov)
#define pqcrystals_kyber1024_ref_dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_dec)
#define pqcrystals_kyber1024_ref_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_enc)
#define pqcrystals_kyber1024_ref_enc_derand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_enc_derand)
#define pqcrystals_kyber1024_ref_gen_matrix BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_gen_matrix)
#define pqcrystals_kyber1024_ref_indcpa_dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_indcpa_dec)
#define pqcrystals_kyber1024_ref_indcpa_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_indcpa_enc)
#define pqcrystals_kyber1024_ref_indcpa_keypair_derand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_indcpa_keypair_derand)
#define pqcrystals_kyber1024_ref_invntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_invntt)
#define pqcrystals_kyber1024_ref_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_keypair)
#define pqcrystals_kyber1024_ref_keypair_derand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_keypair_derand)
#define pqcrystals_kyber1024_ref_kyber_shake128_absorb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_kyber_shake128_absorb)
#define pqcrystals_kyber1024_ref_kyber_shake256_prf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_kyber_shake256_prf)
#define pqcrystals_kyber1024_ref_montgomery_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_montgomery_reduce)
#define pqcrystals_kyber1024_ref_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_ntt)
#define pqcrystals_kyber1024_ref_poly_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_add)
#define pqcrystals_kyber1024_ref_poly_basemul_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_basemul_montgomery)
#define pqcrystals_kyber1024_ref_poly_cbd_eta1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_cbd_eta1)
#define pqcrystals_kyber1024_ref_poly_cbd_eta2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_cbd_eta2)
#define pqcrystals_kyber1024_ref_poly_compress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_compress)
#define pqcrystals_kyber1024_ref_poly_decompress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_decompress)
#define pqcrystals_kyber1024_ref_poly_frombytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_frombytes)
#define pqcrystals_kyber1024_ref_poly_frommsg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_frommsg)
#define pqcrystals_kyber1024_ref_poly_getnoise_eta1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_getnoise_eta1)
#define pqcrystals_kyber1024_ref_poly_getnoise_eta2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_getnoise_eta2)
#define pqcrystals_kyber1024_ref_poly_invntt_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_invntt_tomont)
#define pqcrystals_kyber1024_ref_poly_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_ntt)
#define pqcrystals_kyber1024_ref_poly_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_reduce)
#define pqcrystals_kyber1024_ref_poly_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_sub)
#define pqcrystals_kyber1024_ref_poly_tobytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_tobytes)
#define pqcrystals_kyber1024_ref_poly_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_tomont)
#define pqcrystals_kyber1024_ref_poly_tomsg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_poly_tomsg)
#define pqcrystals_kyber1024_ref_polyvec_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_polyvec_add)
#define pqcrystals_kyber1024_ref_polyvec_basemul_acc_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_polyvec_basemul_acc_montgomery)
#define pqcrystals_kyber1024_ref_polyvec_compress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_polyvec_compress)
#define pqcrystals_kyber1024_ref_polyvec_decompress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_polyvec_decompress)
#define pqcrystals_kyber1024_ref_polyvec_frombytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_polyvec_frombytes)
#define pqcrystals_kyber1024_ref_polyvec_invntt_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_polyvec_invntt_tomont)
#define pqcrystals_kyber1024_ref_polyvec_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_polyvec_ntt)
#define pqcrystals_kyber1024_ref_polyvec_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_polyvec_reduce)
#define pqcrystals_kyber1024_ref_polyvec_tobytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_polyvec_tobytes)
#define pqcrystals_kyber1024_ref_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_verify)
#define pqcrystals_kyber1024_ref_zetas BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber1024_ref_zetas)
#define pqcrystals_kyber512_ref_barrett_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_barrett_reduce)
#define pqcrystals_kyber512_ref_basemul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_basemul)
#define pqcrystals_kyber512_ref_cmov BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_cmov)
#define pqcrystals_kyber512_ref_dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_dec)
#define pqcrystals_kyber512_ref_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_enc)
#define pqcrystals_kyber512_ref_enc_derand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_enc_derand)
#define pqcrystals_kyber512_ref_gen_matrix BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_gen_matrix)
#define pqcrystals_kyber512_ref_indcpa_dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_indcpa_dec)
#define pqcrystals_kyber512_ref_indcpa_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_indcpa_enc)
#define pqcrystals_kyber512_ref_indcpa_keypair_derand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_indcpa_keypair_derand)
#define pqcrystals_kyber512_ref_invntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_invntt)
#define pqcrystals_kyber512_ref_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_keypair)
#define pqcrystals_kyber512_ref_keypair_derand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_keypair_derand)
#define pqcrystals_kyber512_ref_kyber_shake128_absorb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_kyber_shake128_absorb)
#define pqcrystals_kyber512_ref_kyber_shake256_prf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_kyber_shake256_prf)
#define pqcrystals_kyber512_ref_montgomery_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_montgomery_reduce)
#define pqcrystals_kyber512_ref_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_ntt)
#define pqcrystals_kyber512_ref_poly_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_add)
#define pqcrystals_kyber512_ref_poly_basemul_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_basemul_montgomery)
#define pqcrystals_kyber512_ref_poly_cbd_eta1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_cbd_eta1)
#define pqcrystals_kyber512_ref_poly_cbd_eta2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_cbd_eta2)
#define pqcrystals_kyber512_ref_poly_compress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_compress)
#define pqcrystals_kyber512_ref_poly_decompress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_decompress)
#define pqcrystals_kyber512_ref_poly_frombytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_frombytes)
#define pqcrystals_kyber512_ref_poly_frommsg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_frommsg)
#define pqcrystals_kyber512_ref_poly_getnoise_eta1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_getnoise_eta1)
#define pqcrystals_kyber512_ref_poly_getnoise_eta2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_getnoise_eta2)
#define pqcrystals_kyber512_ref_poly_invntt_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_invntt_tomont)
#define pqcrystals_kyber512_ref_poly_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_ntt)
#define pqcrystals_kyber512_ref_poly_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_reduce)
#define pqcrystals_kyber512_ref_poly_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_sub)
#define pqcrystals_kyber512_ref_poly_tobytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_tobytes)
#define pqcrystals_kyber512_ref_poly_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_tomont)
#define pqcrystals_kyber512_ref_poly_tomsg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_poly_tomsg)
#define pqcrystals_kyber512_ref_polyvec_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_polyvec_add)
#define pqcrystals_kyber512_ref_polyvec_basemul_acc_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_polyvec_basemul_acc_montgomery)
#define pqcrystals_kyber512_ref_polyvec_compress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_polyvec_compress)
#define pqcrystals_kyber512_ref_polyvec_decompress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_polyvec_decompress)
#define pqcrystals_kyber512_ref_polyvec_frombytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_polyvec_frombytes)
#define pqcrystals_kyber512_ref_polyvec_invntt_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_polyvec_invntt_tomont)
#define pqcrystals_kyber512_ref_polyvec_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_polyvec_ntt)
#define pqcrystals_kyber512_ref_polyvec_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_polyvec_reduce)
#define pqcrystals_kyber512_ref_polyvec_tobytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_polyvec_tobytes)
#define pqcrystals_kyber512_ref_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_verify)
#define pqcrystals_kyber512_ref_zetas BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber512_ref_zetas)
#define pqcrystals_kyber768_ref_barrett_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_barrett_reduce)
#define pqcrystals_kyber768_ref_basemul BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_basemul)
#define pqcrystals_kyber768_ref_cmov BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_cmov)
#define pqcrystals_kyber768_ref_dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_dec)
#define pqcrystals_kyber768_ref_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_enc)
#define pqcrystals_kyber768_ref_enc_derand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_enc_derand)
#define pqcrystals_kyber768_ref_gen_matrix BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_gen_matrix)
#define pqcrystals_kyber768_ref_indcpa_dec BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_indcpa_dec)
#define pqcrystals_kyber768_ref_indcpa_enc BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_indcpa_enc)
#define pqcrystals_kyber768_ref_indcpa_keypair_derand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_indcpa_keypair_derand)
#define pqcrystals_kyber768_ref_invntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_invntt)
#define pqcrystals_kyber768_ref_keypair BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_keypair)
#define pqcrystals_kyber768_ref_keypair_derand BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_keypair_derand)
#define pqcrystals_kyber768_ref_kyber_shake128_absorb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_kyber_shake128_absorb)
#define pqcrystals_kyber768_ref_kyber_shake256_prf BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_kyber_shake256_prf)
#define pqcrystals_kyber768_ref_montgomery_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_montgomery_reduce)
#define pqcrystals_kyber768_ref_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_ntt)
#define pqcrystals_kyber768_ref_poly_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_add)
#define pqcrystals_kyber768_ref_poly_basemul_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_basemul_montgomery)
#define pqcrystals_kyber768_ref_poly_cbd_eta1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_cbd_eta1)
#define pqcrystals_kyber768_ref_poly_cbd_eta2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_cbd_eta2)
#define pqcrystals_kyber768_ref_poly_compress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_compress)
#define pqcrystals_kyber768_ref_poly_decompress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_decompress)
#define pqcrystals_kyber768_ref_poly_frombytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_frombytes)
#define pqcrystals_kyber768_ref_poly_frommsg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_frommsg)
#define pqcrystals_kyber768_ref_poly_getnoise_eta1 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_getnoise_eta1)
#define pqcrystals_kyber768_ref_poly_getnoise_eta2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_getnoise_eta2)
#define pqcrystals_kyber768_ref_poly_invntt_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_invntt_tomont)
#define pqcrystals_kyber768_ref_poly_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_ntt)
#define pqcrystals_kyber768_ref_poly_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_reduce)
#define pqcrystals_kyber768_ref_poly_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_sub)
#define pqcrystals_kyber768_ref_poly_tobytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_tobytes)
#define pqcrystals_kyber768_ref_poly_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_tomont)
#define pqcrystals_kyber768_ref_poly_tomsg BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_poly_tomsg)
#define pqcrystals_kyber768_ref_polyvec_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_polyvec_add)
#define pqcrystals_kyber768_ref_polyvec_basemul_acc_montgomery BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_polyvec_basemul_acc_montgomery)
#define pqcrystals_kyber768_ref_polyvec_compress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_polyvec_compress)
#define pqcrystals_kyber768_ref_polyvec_decompress BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_polyvec_decompress)
#define pqcrystals_kyber768_ref_polyvec_frombytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_polyvec_frombytes)
#define pqcrystals_kyber768_ref_polyvec_invntt_tomont BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_polyvec_invntt_tomont)
#define pqcrystals_kyber768_ref_polyvec_ntt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_polyvec_ntt)
#define pqcrystals_kyber768_ref_polyvec_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_polyvec_reduce)
#define pqcrystals_kyber768_ref_polyvec_tobytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_polyvec_tobytes)
#define pqcrystals_kyber768_ref_verify BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_verify)
#define pqcrystals_kyber768_ref_zetas BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber768_ref_zetas)
#define pqcrystals_kyber_fips202_ref_sha3_256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_sha3_256)
#define pqcrystals_kyber_fips202_ref_sha3_512 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_sha3_512)
#define pqcrystals_kyber_fips202_ref_shake128 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake128)
#define pqcrystals_kyber_fips202_ref_shake128_absorb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake128_absorb)
#define pqcrystals_kyber_fips202_ref_shake128_absorb_once BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake128_absorb_once)
#define pqcrystals_kyber_fips202_ref_shake128_finalize BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake128_finalize)
#define pqcrystals_kyber_fips202_ref_shake128_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake128_init)
#define pqcrystals_kyber_fips202_ref_shake128_squeeze BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake128_squeeze)
#define pqcrystals_kyber_fips202_ref_shake128_squeezeblocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake128_squeezeblocks)
#define pqcrystals_kyber_fips202_ref_shake256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake256)
#define pqcrystals_kyber_fips202_ref_shake256_absorb BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake256_absorb)
#define pqcrystals_kyber_fips202_ref_shake256_absorb_once BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake256_absorb_once)
#define pqcrystals_kyber_fips202_ref_shake256_finalize BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake256_finalize)
#define pqcrystals_kyber_fips202_ref_shake256_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake256_init)
#define pqcrystals_kyber_fips202_ref_shake256_squeeze BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake256_squeeze)
#define pqcrystals_kyber_fips202_ref_shake256_squeezeblocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqcrystals_kyber_fips202_ref_shake256_squeezeblocks)
#define pqdsa_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, pqdsa_asn1_meth)
#define rand_fork_unsafe_buffering_enabled BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rand_fork_unsafe_buffering_enabled)
#define rsa_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_asn1_meth)
#define rsa_default_private_transform BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_default_private_transform)
#define rsa_default_sign_raw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_default_sign_raw)
#define rsa_default_size BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_default_size)
#define rsa_digestsign_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_digestsign_no_self_test)
#define rsa_digestverify_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_digestverify_no_self_test)
#define rsa_invalidate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_invalidate_key)
#define rsa_private_transform BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_private_transform)
#define rsa_private_transform_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_private_transform_no_self_test)
#define rsa_pss_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_pss_asn1_meth)
#define rsa_sign_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_sign_no_self_test)
#define rsa_verify_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_verify_no_self_test)
#define rsa_verify_raw_no_self_test BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsa_verify_raw_no_self_test)
#define rsaz_1024_gather5_avx2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_1024_gather5_avx2)
#define rsaz_1024_mul_avx2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_1024_mul_avx2)
#define rsaz_1024_norm2red_avx2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_1024_norm2red_avx2)
#define rsaz_1024_red2norm_avx2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_1024_red2norm_avx2)
#define rsaz_1024_scatter5_avx2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_1024_scatter5_avx2)
#define rsaz_1024_sqr_avx2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_1024_sqr_avx2)
#define rsaz_amm52x20_x1_ifma256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_amm52x20_x1_ifma256)
#define rsaz_amm52x20_x2_ifma256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_amm52x20_x2_ifma256)
#define rsaz_amm52x30_x1_ifma256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_amm52x30_x1_ifma256)
#define rsaz_amm52x30_x2_ifma256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_amm52x30_x2_ifma256)
#define rsaz_amm52x40_x1_ifma256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_amm52x40_x1_ifma256)
#define rsaz_amm52x40_x2_ifma256 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, rsaz_amm52x40_x2_ifma256)
#define s2i_ASN1_INTEGER BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, s2i_ASN1_INTEGER)
#define s2i_ASN1_OCTET_STRING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, s2i_ASN1_OCTET_STRING)
#define sha1_block_data_order BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha1_block_data_order)
#define sha1_block_data_order_avx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha1_block_data_order_avx)
#define sha1_block_data_order_avx2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha1_block_data_order_avx2)
#define sha1_block_data_order_hw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha1_block_data_order_hw)
#define sha1_block_data_order_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha1_block_data_order_neon)
#define sha1_block_data_order_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha1_block_data_order_nohw)
#define sha1_block_data_order_ssse3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha1_block_data_order_ssse3)
#define sha1_func BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha1_func)
#define sha224_func BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha224_func)
#define sha256_block_data_order_avx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha256_block_data_order_avx)
#define sha256_block_data_order_hw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha256_block_data_order_hw)
#define sha256_block_data_order_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha256_block_data_order_neon)
#define sha256_block_data_order_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha256_block_data_order_nohw)
#define sha256_block_data_order_ssse3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha256_block_data_order_ssse3)
#define sha256_func BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha256_func)
#define sha384_func BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha384_func)
#define sha512_block_data_order BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha512_block_data_order)
#define sha512_block_data_order_avx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha512_block_data_order_avx)
#define sha512_block_data_order_hw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha512_block_data_order_hw)
#define sha512_block_data_order_neon BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha512_block_data_order_neon)
#define sha512_block_data_order_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha512_block_data_order_nohw)
#define sha512_func BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sha512_func)
#define sk_pop_free BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sk_pop_free)
#define sskdf_variant_digest BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sskdf_variant_digest)
#define sskdf_variant_hmac BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, sskdf_variant_hmac)
#define used_for_hmac BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, used_for_hmac)
#define v2i_GENERAL_NAME BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v2i_GENERAL_NAME)
#define v2i_GENERAL_NAMES BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v2i_GENERAL_NAMES)
#define v2i_GENERAL_NAME_ex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v2i_GENERAL_NAME_ex)
#define v3_akey_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_akey_id)
#define v3_alt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_alt)
#define v3_bcons BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_bcons)
#define v3_cpols BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_cpols)
#define v3_crl_invdate BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_crl_invdate)
#define v3_crl_num BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_crl_num)
#define v3_crl_reason BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_crl_reason)
#define v3_crld BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_crld)
#define v3_delta_crl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_delta_crl)
#define v3_ext_ku BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_ext_ku)
#define v3_freshest_crl BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_freshest_crl)
#define v3_idp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_idp)
#define v3_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_info)
#define v3_inhibit_anyp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_inhibit_anyp)
#define v3_key_usage BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_key_usage)
#define v3_name_constraints BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_name_constraints)
#define v3_ns_ia5_list BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_ns_ia5_list)
#define v3_nscert BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_nscert)
#define v3_ocsp_accresp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_ocsp_accresp)
#define v3_ocsp_nocheck BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_ocsp_nocheck)
#define v3_ocsp_nonce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_ocsp_nonce)
#define v3_policy_constraints BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_policy_constraints)
#define v3_policy_mappings BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_policy_mappings)
#define v3_sinfo BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_sinfo)
#define v3_skey_id BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, v3_skey_id)
#define voprf_exp2_blind BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_exp2_blind)
#define voprf_exp2_client_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_exp2_client_key_from_bytes)
#define voprf_exp2_derive_key_from_secret BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_exp2_derive_key_from_secret)
#define voprf_exp2_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_exp2_generate_key)
#define voprf_exp2_issuer_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_exp2_issuer_key_from_bytes)
#define voprf_exp2_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_exp2_read)
#define voprf_exp2_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_exp2_sign)
#define voprf_exp2_unblind BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_exp2_unblind)
#define voprf_pst1_blind BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_pst1_blind)
#define voprf_pst1_client_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_pst1_client_key_from_bytes)
#define voprf_pst1_derive_key_from_secret BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_pst1_derive_key_from_secret)
#define voprf_pst1_generate_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_pst1_generate_key)
#define voprf_pst1_issuer_key_from_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_pst1_issuer_key_from_bytes)
#define voprf_pst1_read BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_pst1_read)
#define voprf_pst1_sign BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_pst1_sign)
#define voprf_pst1_sign_with_proof_scalar_for_testing BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_pst1_sign_with_proof_scalar_for_testing)
#define voprf_pst1_unblind BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, voprf_pst1_unblind)
#define vpaes_cbc_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, vpaes_cbc_encrypt)
#define vpaes_ctr32_encrypt_blocks BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, vpaes_ctr32_encrypt_blocks)
#define vpaes_decrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, vpaes_decrypt)
#define vpaes_decrypt_key_to_bsaes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, vpaes_decrypt_key_to_bsaes)
#define vpaes_encrypt BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, vpaes_encrypt)
#define vpaes_encrypt_key_to_bsaes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, vpaes_encrypt_key_to_bsaes)
#define vpaes_set_decrypt_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, vpaes_set_decrypt_key)
#define vpaes_set_encrypt_key BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, vpaes_set_encrypt_key)
#define x25519_asn1_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_asn1_meth)
#define x25519_ge_add BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_ge_add)
#define x25519_ge_frombytes_vartime BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_ge_frombytes_vartime)
#define x25519_ge_p1p1_to_p2 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_ge_p1p1_to_p2)
#define x25519_ge_p1p1_to_p3 BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_ge_p1p1_to_p3)
#define x25519_ge_p3_to_cached BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_ge_p3_to_cached)
#define x25519_ge_scalarmult BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_ge_scalarmult)
#define x25519_ge_scalarmult_base BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_ge_scalarmult_base)
#define x25519_ge_scalarmult_small_precomp BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_ge_scalarmult_small_precomp)
#define x25519_ge_sub BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_ge_sub)
#define x25519_ge_tobytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_ge_tobytes)
#define x25519_pkey_meth BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_pkey_meth)
#define x25519_public_from_private_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_public_from_private_nohw)
#define x25519_public_from_private_s2n_bignum BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_public_from_private_s2n_bignum)
#define x25519_sc_reduce BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_sc_reduce)
#define x25519_scalar_mult_generic_nohw BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_scalar_mult_generic_nohw)
#define x25519_scalar_mult_generic_s2n_bignum BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x25519_scalar_mult_generic_s2n_bignum)
#define x509V3_add_value_asn1_string BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509V3_add_value_asn1_string)
#define x509_check_cert_time BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509_check_cert_time)
#define x509_check_issued_with_callback BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509_check_issued_with_callback)
#define x509_digest_sign_algorithm BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509_digest_sign_algorithm)
#define x509_digest_verify_init BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509_digest_verify_init)
#define x509_init_signature_info BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509_init_signature_info)
#define x509_print_rsa_pss_params BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509_print_rsa_pss_params)
#define x509_rsa_ctx_to_pss BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509_rsa_ctx_to_pss)
#define x509_rsa_pss_to_ctx BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509_rsa_pss_to_ctx)
#define x509v3_a2i_ipadd BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509v3_a2i_ipadd)
#define x509v3_bytes_to_hex BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509v3_bytes_to_hex)
#define x509v3_cache_extensions BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509v3_cache_extensions)
#define x509v3_conf_name_matches BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509v3_conf_name_matches)
#define x509v3_ext_free_with_method BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509v3_ext_free_with_method)
#define x509v3_hex_to_bytes BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509v3_hex_to_bytes)
#define x509v3_looks_like_dns_name BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x509v3_looks_like_dns_name)
#define x86_64_assembly_implementation_FOR_TESTING BORINGSSL_ADD_PREFIX(BORINGSSL_PREFIX, x86_64_assembly_implementation_FOR_TESTING)

#endif // BORINGSSL_PREFIX_SYMBOLS_H
