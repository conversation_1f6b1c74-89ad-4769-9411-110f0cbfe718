# WIPO Patent Crawler

## 階層

src/
├── domains/           # ドメイン層・ビジネスロジック
│   ├── patent.rs      # 特許データ構造体
│   └── designated_states.rs  # 指定国管理
├── handler/           # インフラ層・ユーティリティ
│   ├── html_parser.rs # HTML解析
│   ├── pdf_downloader.rs # PDFダウンロード
│   └── browser_handler.rs # ブラウザ操作
├── jobs/              # アプリケーション層・バッチ処理
│   ├── patent_processor.rs # 特許処理
│   └── sitemap_processor.rs # サイトマップ処理
└── main.rs            # エントリーポイント

## コンテナ

### 開発用（ローカルファイル使用）
```sh
cd docker
docker compose up --build
# または
make run-dev
```

### ECS用ローカルテスト（AWS認証情報付き）
```sh
# AWS_PROFILE=opendata-sandbox を使用してS3にアップロード
make run-ecs-test
# または
cd docker
docker compose -f docker-compose.ecs.yml up --build wipo-crawler-ecs-test
```

### ECS本番環境用（IAMロール使用）
```sh
# IAMロールを使用してS3にアップロード（AWS_PROFILE不要）
make run-ecs-prod
# または
cd docker
docker compose -f docker-compose.ecs.yml up --build wipo-crawler-ecs-prod
```

### デモモード（LIMIT=1、ローカル保存）
```sh
make run-demo
# または
cd docker
docker compose -f docker-compose.ecs.yml up --build wipo-crawler-demo
```

## テスト

```
curl -o Patentscope_sitemap.xml https://patentscope.wipo.int/sitemap/Patentscope_sitemap.xml
curl -o Patentscope202506.xml https://patentscope.wipo.int/sitemap/en/sitemap_Patentscope202506.xml
```

### browser.rs のテスト

```sh
# tests ファイルにあるサイトマップ、XMLファイル、対象のHTMLを用意すると実行可能
./tests
├── Patentscope202504.xml
├── Patentscope_sitemap.xml
└── WO2025113830.html

DEMO=true IS_LOCAL=true cargo run

# 特定の件数だけスクレイピングする場合
LIMIT=5 cargo run
```

## 環境変数

### 基本設定
- `DEMO`: `true`でデモモード（ローカルファイル使用）、`false`で本番モード
- `IS_LOCAL`: `true`でローカル保存、`false`でS3保存
- `PATENT_DAYS`: 取得する特許の日数（デフォルト: 7）
- `LIMIT`: 処理する特許の件数制限（テスト用）

### AWS設定（DEMO=false かつ IS_LOCAL=false の場合のみ必要）
- `AWS_PROFILE`: AWSプロファイル名（ローカルテスト時のみ、例: `opendata-sandbox`）
- `AWS_REGION`: AWSリージョン（デフォルト: `ap-northeast-1`）
- `ADP_BUCKET_NAME`: S3バケット名（例: `adp-workflow-interface-sandbox`）

### 使用例

#### ローカル開発
```sh
DEMO=true IS_LOCAL=true LIMIT=1 cargo run
```

#### ECS用ローカルテスト

```sh
AWS_PROFILE=opendata-sandbox DEMO=false IS_LOCAL=false LIMIT=1 \
AWS_REGION=ap-northeast-1 ADP_BUCKET_NAME=adp-workflow-interface-sandbox \
cargo run
```

## ECS 起動

```sh
aws ecs run-task --region ap-northeast-1 --profile opendata-sandbox \
    --cluster sandbox-cluster \
    --task-definition sandbox-patent-scraper \
    --count 1 \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[subnet-0953cbf4a5121ba36],securityGroups=[sg-08b92bcd6ca41cecf],assignPublicIp=ENABLED}"
```
