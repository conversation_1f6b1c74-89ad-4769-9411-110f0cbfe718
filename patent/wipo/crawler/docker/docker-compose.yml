services:
  wipo-crawler-dev:
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    environment:
      - DEMO=true
      - IS_LOCAL=true
      - PATENT_DAYS=7
      - LIMIT=1
    volumes:
      - ..:/app
      - cargo-cache:/usr/local/cargo/registry
      - target-cache:/app/target
    working_dir: /app

  # WIPO のサイトから1件だけデータを取得する
  wipo-crawler-test:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    environment:
      - IS_LOCAL=true
      - PATENT_DAYS=7
      - LIMIT=1
      - AWS_PROFILE=opendata-sandbox
      - ADP_BUCKET_NAME=adp-workflow-interface-sandbox
    volumes:
      - ~/.aws:/root/.aws:ro  # AWS認証情報をマウント
      - ../sample:/app/sample  # 出力ディレクトリをマウント
    working_dir: /app

volumes:
  cargo-cache:
  target-cache: