pub mod browser_handler;
pub mod html_parser;
pub mod pdf_downloader;
pub mod parquet_writer;
pub mod csv_writer;
pub mod s3_handler;

pub use self::{
    browser_handler::{
        close_tab, new_browser, navigate_to_claims_tab, navigate_to_documents_tab,
        navigate_to_patent_detail,
    },
    html_parser::{
        extract_claims_data_from_document, extract_patent_data_from_document,
    },
    pdf_downloader::extract_pdf_url,
    parquet_writer::save_patent_data_to_parquet,
    csv_writer::{generate_csv_data, save_as_csv},
    s3_handler::{
        upload_file_to_s3, upload_csv_data_to_s3, upload_parquet_data_to_s3,
    },
};