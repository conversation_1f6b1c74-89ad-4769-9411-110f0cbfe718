use anyhow::Result;
use aws_sdk_s3::{config::Region, Client, primitives::ByteStream};
use log::info;

pub async fn upload_file_to_s3(file_path: &str, region: &str, bucket: &str, key: &str) -> Result<String, Box<dyn std::error::Error>> {
    let region = Region::new(region.to_string());
    let config = aws_config::defaults(aws_config::BehaviorVersion::latest()).region(region).load().await;
    let client = Client::new(&config);

    let file_content = tokio::fs::read(file_path).await?;
    let body = ByteStream::from(file_content);
    let request = client.put_object().bucket(bucket).key(key).body(body);
    let response = request.send().await?;

    Ok(response.e_tag().unwrap_or_default().to_string())
}

/// CSVデータをS3にアップロードする関数
pub async fn upload_csv_data_to_s3(csv_data: &[u8], region: &str, bucket: &str, key: &str) -> Result<String, Box<dyn std::error::Error>> {
    let region = Region::new(region.to_string());
    let config = aws_config::defaults(aws_config::BehaviorVersion::latest()).region(region).load().await;
    let client = Client::new(&config);

    let body = ByteStream::from(csv_data.to_vec());
    let request = client.put_object().bucket(bucket).key(key).body(body);
    let response = request.send().await?;

    Ok(response.e_tag().unwrap_or_default().to_string())
}

/// Parquetデータをメモリ上からS3にアップロードする関数
pub async fn upload_parquet_data_to_s3(parquet_data: &[u8], region: &str, bucket: &str, key: &str) -> Result<String, Box<dyn std::error::Error>> {
    let region = Region::new(region.to_string());
    let config = aws_config::defaults(aws_config::BehaviorVersion::latest()).region(region).load().await;
    let client = Client::new(&config);

    let body = ByteStream::from(parquet_data.to_vec());
    let request = client.put_object().bucket(bucket).key(key).body(body);
    let response = request.send().await?;

    Ok(response.e_tag().unwrap_or_default().to_string())
}
