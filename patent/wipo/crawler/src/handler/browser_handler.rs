use anyhow::Result;
use headless_chrome::{<PERSON><PERSON><PERSON>, LaunchOptionsBuilder, Tab};
use log::info;
use std::sync::Arc;
use std::time::Duration;

/// 新しいブラウザを作成する
pub fn new_browser() -> Result<Browser> {
    let launch_options = LaunchOptionsBuilder::default()
        .headless(true)
        .idle_browser_timeout(Duration::from_secs(300)) // 5分のタイムアウト
        .sandbox(false) // サンドボックスを無効化して安定性を向上
        .build()?;
    
    Browser::new(launch_options)
}

/// 新しいタブを作成して設定する
pub fn create_tab(browser: &Browser) -> Result<Arc<Tab>> {
    let tab = browser.new_tab()?;
    tab.navigate_to("about:blank")?;
    tab.wait_until_navigated()?;
    
    // ユーザーエージェントを設定
    tab.set_user_agent(
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", 
        Some("en-US,en;q=0.9"), 
        Some("macOS")
    )?;
    
    Ok(tab)
}

/// 特許詳細ページにナビゲートする
pub fn navigate_to_patent_detail(browser: &Browser, wo_number: &str) -> Result<Arc<Tab>> {
    let url = format!("https://patentscope2.wipo.int/search/en/detail.jsf?docId={}", wo_number);
    let tab = create_tab(browser)?;
    
    info!("Navigating to patent detail: {}", url);
    tab.navigate_to(&url)?;

    // ページの読み込みを待機（より長いタイムアウト）
    tab.wait_until_navigated()?;
    
    // ページが完全に読み込まれるまで少し待機
    std::thread::sleep(Duration::from_secs(3));
    
    // 複数の要素セレクタを試して、どれかが見つかればOK
    let selectors = [
        "div.ps-field-label",
        "span.ps-field--label", 
        "div.ps-biblio-data",
        "span.ps-field--value",
        "div.ps-panel--content"
    ];
    
    let mut element_found = false;
    for selector in &selectors {
        if let Ok(_) = tab.wait_for_element(selector) {
            element_found = true;
            break;
        }
    }
    
    if !element_found {
        info!("Warning: No expected elements found, but continuing anyway");
    }
    
    Ok(tab)
}

/// Documents タブをクリックしてPDFリンクを取得する
pub fn navigate_to_documents_tab(tab: &mut Arc<Tab>) -> Result<()> {
    let documents_tab_selector = "a[href*='PCTDOCUMENTS']";
    
    if let Ok(documents_tab) = tab.wait_for_element(documents_tab_selector) {
        // タブをクリック
        documents_tab.click()?;
        
        // Documents タブの内容が読み込まれるまで待機
        // タブ切り替え後のコンテンツが表示されるまで少し待機
        std::thread::sleep(Duration::from_millis(2000));
        
        // PDF リンクが表示されるまで待機
        if let Ok(_) = tab.wait_for_element("a[href$='.pdf']") {
            info!("Documents tab loaded successfully");
            Ok(())
        } else {
            info!("PDF links not found in documents tab");
            Ok(())
        }
    } else {
        info!("Documents tab not found");
        Ok(())
    }
}

/// Claims タブをクリックして請求項情報を取得する
pub fn navigate_to_claims_tab(tab: &mut Arc<Tab>) -> Result<()> {
    let tab_selector = "a[href*='PCTCLAIMS']";
    if let Ok(claims_tab) = tab.wait_for_element(tab_selector) {
        claims_tab.click()?;
        tab.wait_for_element("p")?;

        // <p>CLAIMS</p>がHTMLに現れるまでループで待つ
        let mut attempts = 0;
        let max_attempts = 20;
        while attempts < max_attempts {
            let html = tab.get_content()?;
            if html.contains("<p>CLAIMS</p>") {
                info!("Found <p>CLAIMS</p> in HTML");
                break;
            }
            std::thread::sleep(std::time::Duration::from_millis(1000));
            attempts += 1;
        }
        if attempts == max_attempts {
            info!("Timeout: <p>CLAIMS</p> not found in HTML");
        }
    } else {
        info!("'Claims' tab not found.");
    }
    Ok(())
}

/// タブを閉じる
pub fn close_tab(tab: Arc<Tab>) -> Result<()> {
    tab.close(true)?;
    Ok(())
} 